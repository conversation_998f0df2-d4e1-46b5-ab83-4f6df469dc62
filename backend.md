req.params
centerId
HELLO: 682e257644f4dce64bfee3f6
[
  {
    _id: new ObjectId('6877f55558313742b2099661'),
    center: {
      _id: new ObjectId('682e257644f4dce64bfee3f6'),
      legalName: 'Brianjjjggyg',
      displayName: 'briangjkkk',
      address: [Object],
      companyNumber: ''
    },
    student: [],
    category: 'Language',
    classProviding: 'Brian',
    level: 'beginer',
    description: 'good course',
    mode: false,
    sen: false,
    initialCharge: '0',
    charge: 1,
    ageFrom: 11,
    ageTo: 12,
    minimumStudent: 1,
    numberOfClass: 0,
    languageOptions: [],
    status: 'active',
    rearrangementInfo: {
      isRearranging: false,
      originalDates: [],
      newDates: [],
      studentsNotified: false
    },
    createdAt: 2025-07-16T18:54:13.837Z,
    updatedAt: 2025-07-23T15:14:42.844Z,
    __v: 0,
    coach: {
      mainImage: [Object],
      manager: null,
      legalName: '',
      phone: '',
      rating: 0,
      reviewCount: 0,
      programs: [],
      _id: new ObjectId('67e7af5b22a791ee0bc40fda'),
      baseUser: new ObjectId('67e7af5b22a791ee0bc40fd6'),
      email: '<EMAIL>',
      displayName: 'Coach Ten',
      address: [Object],
      languages: [Array],
      description: 'Cycling coach for competitive athletes',
      ageFrom: '15',
      ageTo: '25',
      sen: false,
      experience: [Array],
      skill: [Array],
      accredation: [Array],
      images: [],
      createdAt: 2025-03-29T08:29:15.632Z,
      updatedAt: 2025-06-24T10:38:28.057Z,
      __v: 0,
      center: new ObjectId('682e257644f4dce64bfee3f6'),
      classzId: 'COA-250619-GTFK'
    }
  },
  {
    _id: new ObjectId('687802dc44de1e6f89803c19'),
    center: {
      _id: new ObjectId('682e257644f4dce64bfee3f6'),
      legalName: 'Brianjjjggyg',
      displayName: 'briangjkkk',
      address: [Object],
      companyNumber: ''
    },
    student: [],
    category: 'Art',
    classProviding: 'I do not know',
    level: 'advance',
    description: '1234567',
    mode: false,
    sen: false,
    initialCharge: '0',
    charge: 1,
    ageFrom: 5,
    ageTo: 15,
    minimumStudent: 1,
    numberOfClass: 0,
    languageOptions: [],
    status: 'active',
    rearrangementInfo: {
      isRearranging: false,
      originalDates: [],
      newDates: [],
      studentsNotified: false
    },
    createdAt: 2025-07-16T19:51:56.288Z,
    updatedAt: 2025-07-23T14:16:28.016Z,
    __v: 0,
    coach: {
      mainImage: [Object],
      _id: new ObjectId('67e7af5122a791ee0bc40fc5'),
      baseUser: new ObjectId('67e7af5122a791ee0bc40fc1'),
      email: '<EMAIL>',
      displayName: 'Coach Seven',
      address: [Object],
      languages: [Array],
      description: 'Expert in rugby coaching',
      ageFrom: '12',
      ageTo: '20',
      sen: false,
      experience: [Array],
      skill: [Array],
      accredation: [Array],
      images: [],
      createdAt: 2025-03-29T08:29:05.491Z,
      updatedAt: 2025-06-24T10:40:31.103Z,
      __v: 0,
      center: new ObjectId('682e257644f4dce64bfee3f6'),
      legalName: '',
      manager: null,
      phone: '',
      programs: [],
      rating: 0,
      reviewCount: 0,
      classzId: 'COA-250619-HFWB'
    }
  },
  {
    mainImage: {
      url: '/uploads/1752758884124-mainImage-682e257644f4dce64bfee3f6.jpg',
      contentType: 'image/jpeg'
    },
    _id: new ObjectId('6878fa6469bd50709da43796'),
    center: {
      _id: new ObjectId('682e257644f4dce64bfee3f6'),
      legalName: 'Brianjjjggyg',
      displayName: 'briangjkkk',
      address: [Object],
      companyNumber: ''
    },
    student: [],
    category: 'Language',
    classProviding: 'test for slot charge',
    level: 'beginner',
    description: 'its a test class not final ,charge in slot',
    mode: false,
    sen: false,
    initialCharge: '0',
    ageFrom: 6,
    ageTo: 18,
    minimumStudent: 1,
    numberOfClass: 1,
    languageOptions: [],
    status: 'active',
    rearrangementInfo: {
      isRearranging: false,
      originalDates: [],
      newDates: [],
      studentsNotified: false
    },
    createdAt: 2025-07-17T13:28:04.134Z,
    updatedAt: 2025-07-17T18:17:10.512Z,
    __v: 0,
    coach: {
      mainImage: [Object],
      _id: new ObjectId('6718a42e9643902397d094c8'),
      baseUser: new ObjectId('671751c6a5265ddcb386e77c'),
      displayName: 'john doe',
      address: [Object],
      languages: null,
      description: null,
      experience: [Array],
      skill: [Array],
      ageFrom: '25',
      ageTo: '40',
      sen: true,
      accredation: [Array],
      images: [],
      createdAt: 2024-10-23T07:22:22.148Z,
      updatedAt: 2025-07-23T14:17:57.947Z,
      __v: 0,
      email: '<EMAIL>',
      programs: [Array],
      rating: 5,
      reviewCount: 0,
      center: null,
      manager: new ObjectId('680aaa9ea7a8843160c47f13'),
      legalName: '',
      phone: '',
      classzId: 'COA-250619-TZIO'
    }
  },
  {
    mainImage: {
      url: '/uploads/1753001437006-mainImage-682e257644f4dce64bfee3f6.jpg',
      contentType: 'image/jpeg'
    },
    initialCharge: '0',
    _id: new ObjectId('687cadddc0d011786292375c'),
    center: {
      _id: new ObjectId('682e257644f4dce64bfee3f6'),
      legalName: 'Brianjjjggyg',
      displayName: 'briangjkkk',
      address: [Object],
      companyNumber: ''
    },
    category: 'Language',
    classProviding: 'new change for class',
    level: 'beginner',
    description: 'new schema for class',
    mode: false,
    sen: false,
    ageFrom: 5,
    ageTo: 18,
    createdAt: 2025-07-20T08:50:37.019Z,
    updatedAt: 2025-07-20T12:47:20.510Z,
    __v: 0,
    coach: {
      mainImage: [Object],
      _id: new ObjectId('67e7af4322a791ee0bc40fa9'),
      baseUser: new ObjectId('67e7af4322a791ee0bc40fa5'),
      email: '<EMAIL>',
      displayName: 'Coach Three',
      address: [Object],
      languages: [Array],
      description: 'Certified swimmingxz coach',
      ageFrom: '5',
      ageTo: '15',
      sen: false,
      experience: [Array],
      skill: [Array],
      accredation: [Array],
      images: [Array],
      createdAt: 2025-03-29T08:28:51.701Z,
      updatedAt: 2025-07-23T14:24:38.184Z,
      __v: 0,
      legalName: 'N N Dipu',
      manager: new ObjectId('682e257644f4dce64bfee3f6'),
      phone: '',
      programs: [],
      rating: 3.5,
      reviewCount: 0,
      center: new ObjectId('682e257644f4dce64bfee3f6'),
      classzId: 'COA-250619-D69K'
    }
  },
  {
    mainImage: {
      url: '/uploads/1753032836003-mainImage-682e257644f4dce64bfee3f6.jpg',
      contentType: 'image/jpeg'
    },
    initialCharge: '0',
    _id: new ObjectId('687d2884c6f1f8d8a91d58f5'),
    center: {
      _id: new ObjectId('682e257644f4dce64bfee3f6'),
      legalName: 'Brianjjjggyg',
      displayName: 'briangjkkk',
      address: [Object],
      companyNumber: ''
    },
    category: 'Coding',
    classProviding: '1234',
    level: 'beginner',
    description: '12345',
    mode: false,
    sen: false,
    ageFrom: 12,
    ageTo: 15,
    createdAt: 2025-07-20T17:33:56.026Z,
    updatedAt: 2025-07-20T18:02:19.460Z,
    __v: 0,
    coach: {
      mainImage: [Object],
      _id: new ObjectId('67e7af4322a791ee0bc40fa9'),
      baseUser: new ObjectId('67e7af4322a791ee0bc40fa5'),
      email: '<EMAIL>',
      displayName: 'Coach Three',
      address: [Object],
      languages: [Array],
      description: 'Certified swimmingxz coach',
      ageFrom: '5',
      ageTo: '15',
      sen: false,
      experience: [Array],
      skill: [Array],
      accredation: [Array],
      images: [Array],
      createdAt: 2025-03-29T08:28:51.701Z,
      updatedAt: 2025-07-23T14:24:38.184Z,
      __v: 0,
      legalName: 'N N Dipu',
      manager: new ObjectId('682e257644f4dce64bfee3f6'),
      phone: '',
      programs: [],
      rating: 3.5,
      reviewCount: 0,
      center: new ObjectId('682e257644f4dce64bfee3f6'),
      classzId: 'COA-250619-D69K'
    }
  },
  {
    mainImage: {
      url: '/uploads/1753284246019-mainImage-682e257644f4dce64bfee3f6.png',
      contentType: 'image/png'
    },
    initialCharge: '0',
    _id: new ObjectId('6880fe96cdbec5498a0d1847'),
    center: {
      _id: new ObjectId('682e257644f4dce64bfee3f6'),
      legalName: 'Brianjjjggyg',
      displayName: 'briangjkkk',
      address: [Object],
      companyNumber: ''
    },
    category: 'Health & Fitness',
    classProviding: 'coding ai',
    level: 'baby',
    description: 'this is about coding and ai in junior students',
    mode: false,
    sen: true,
    ageFrom: 11,
    ageTo: 14,
    createdAt: 2025-07-23T15:24:06.036Z,
    updatedAt: 2025-07-23T15:51:18.883Z,
    __v: 0,
    coach: {
      mainImage: [Object],
      _id: new ObjectId('67e7af4322a791ee0bc40fa9'),
      baseUser: new ObjectId('67e7af4322a791ee0bc40fa5'),
      email: '<EMAIL>',
      displayName: 'Coach Three',
      address: [Object],
      languages: [Array],
      description: 'Certified swimmingxz coach',
      ageFrom: '5',
      ageTo: '15',
      sen: false,
      experience: [Array],
      skill: [Array],
      accredation: [Array],
      images: [Array],
      createdAt: 2025-03-29T08:28:51.701Z,
      updatedAt: 2025-07-23T14:24:38.184Z,
      __v: 0,
      legalName: 'N N Dipu',
      manager: new ObjectId('682e257644f4dce64bfee3f6'),
      phone: '',
      programs: [],
      rating: 3.5,
      reviewCount: 0,
      center: new ObjectId('682e257644f4dce64bfee3f6'),
      classzId: 'COA-250619-D69K'
    }
  },
  {
    mainImage: {
      url: '/uploads/1753378324710-mainImage-682e257644f4dce64bfee3f6.jpg',
      contentType: 'image/jpeg'
    },
    initialCharge: '0',
    _id: new ObjectId('68826e1428459dc724569fe1'),
    center: {
      _id: new ObjectId('682e257644f4dce64bfee3f6'),
      legalName: 'Brianjjjggyg',
      displayName: 'briangjkkk',
      address: [Object],
      companyNumber: ''
    },
    category: 'Language',
    classProviding: 'aaa',
    level: 'aaa',
    description: '1234',
    mode: false,
    sen: true,
    ageFrom: 10,
    ageTo: 12,
    createdAt: 2025-07-24T17:32:04.737Z,
    updatedAt: 2025-07-24T17:32:04.737Z,
    __v: 0
  },
  {
    mainImage: {
      url: '/uploads/1753378741274-mainImage-682e257644f4dce64bfee3f6.jpg',
      contentType: 'image/jpeg'
    },
    initialCharge: '0',
    _id: new ObjectId('68826fb528459dc72456a003'),
    center: {
      _id: new ObjectId('682e257644f4dce64bfee3f6'),
      legalName: 'Brianjjjggyg',
      displayName: 'briangjkkk',
      address: [Object],
      companyNumber: ''
    },
    category: 'Drama',
    classProviding: '123456',
    level: '12345',
    description: '123456',
    mode: false,
    sen: false,
    ageFrom: 10,
    ageTo: 11,
    createdAt: 2025-07-24T17:39:01.293Z,
    updatedAt: 2025-07-24T17:39:01.293Z,
    __v: 0
  },
  {
    mainImage: {
      url: '/uploads/1753379279414-mainImage-682e257644f4dce64bfee3f6.jpg',
      contentType: 'image/jpeg'
    },
    initialCharge: '0',
    _id: new ObjectId('688271cf28459dc72456a077'),
    center: {
      _id: new ObjectId('682e257644f4dce64bfee3f6'),
      legalName: 'Brianjjjggyg',
      displayName: 'briangjkkk',
      address: [Object],
      companyNumber: ''
    },
    category: 'Health & Fitness',
    classProviding: '12345',
    level: '1234',
    description: '12345',
    mode: true,
    sen: true,
    ageFrom: 11,
    ageTo: 11,
    createdAt: 2025-07-24T17:47:59.497Z,
    updatedAt: 2025-07-24T17:47:59.497Z,
    __v: 0
  },
  {
    mainImage: {
      url: '/uploads/1753390257980-mainImage-682e257644f4dce64bfee3f6.jpg',
      contentType: 'image/jpeg'
    },
    _id: new ObjectId('68829cb10a0606ab8e656e9e'),
    center: {
      _id: new ObjectId('682e257644f4dce64bfee3f6'),
      legalName: 'Brianjjjggyg',
      displayName: 'briangjkkk',
      address: [Object],
      companyNumber: ''
    },
    category: 'Language',
    classProviding: 'test with initial charge',
    level: 'begineer',
    description: 'test',
    mode: false,
    sen: true,
    ageFrom: 10,
    ageTo: 11,
    initialCharge: '13',
    createdAt: 2025-07-24T20:50:57.985Z,
    updatedAt: 2025-07-24T20:53:45.404Z,
    __v: 0,
    coach: {
      mainImage: [Object],
      _id: new ObjectId('67e7af4322a791ee0bc40fa9'),
      baseUser: new ObjectId('67e7af4322a791ee0bc40fa5'),
      email: '<EMAIL>',
      displayName: 'Coach Three',
      address: [Object],
      languages: [Array],
      description: 'Certified swimmingxz coach',
      ageFrom: '5',
      ageTo: '15',
      sen: false,
      experience: [Array],
      skill: [Array],
      accredation: [Array],
      images: [Array],
      createdAt: 2025-03-29T08:28:51.701Z,
      updatedAt: 2025-07-23T14:24:38.184Z,
      __v: 0,
      legalName: 'N N Dipu',
      manager: new ObjectId('682e257644f4dce64bfee3f6'),
      phone: '',
      programs: [],
      rating: 3.5,
      reviewCount: 0,
      center: new ObjectId('682e257644f4dce64bfee3f6'),
      classzId: 'COA-250619-D69K'
    }
  },
  {
    mainImage: {
      url: '/uploads/1753669847509-mainImage-682e257644f4dce64bfee3f6.jpg',
      contentType: 'image/jpeg'
    },
    _id: new ObjectId('6886e0d7f6a7eb40f0b08120'),
    center: {
      _id: new ObjectId('682e257644f4dce64bfee3f6'),
      legalName: 'Brianjjjggyg',
      displayName: 'briangjkkk',
      address: [Object],
      companyNumber: ''
    },
    category: 'Coding',
    classProviding: '12',
    level: '33',
    description: '22',
    mode: false,
    sen: true,
    ageFrom: 11,
    ageTo: 16,
    initialCharge: '5',
    createdAt: 2025-07-28T02:30:47.520Z,
    updatedAt: 2025-07-28T02:36:16.929Z,
    __v: 0,
    coach: {
      mainImage: [Object],
      _id: new ObjectId('67e7af4322a791ee0bc40fa9'),
      baseUser: new ObjectId('67e7af4322a791ee0bc40fa5'),
      email: '<EMAIL>',
      displayName: 'Coach Three',
      address: [Object],
      languages: [Array],
      description: 'Certified swimmingxz coach',
      ageFrom: '5',
      ageTo: '15',
      sen: false,
      experience: [Array],
      skill: [Array],
      accredation: [Array],
      images: [Array],
      createdAt: 2025-03-29T08:28:51.701Z,
      updatedAt: 2025-07-23T14:24:38.184Z,
      __v: 0,
      legalName: 'N N Dipu',
      manager: new ObjectId('682e257644f4dce64bfee3f6'),
      phone: '',
      programs: [],
      rating: 3.5,
      reviewCount: 0,
      center: new ObjectId('682e257644f4dce64bfee3f6'),
      classzId: 'COA-250619-D69K'
    }
  }
]
[SLOW] GET /api/class/center/682e257644f4dce64bfee3f6 - 1654.02ms
[SLOW] GET /api/class/6878fa6469bd50709da43796/slots - 505.63ms
[SLOW] GET /api/class/687cadddc0d011786292375c/slots - 663.40ms
[SLOW] GET /api/class/687d2884c6f1f8d8a91d58f5/slots - 1007.44ms
[SLOW] GET /api/class/6880fe96cdbec5498a0d1847/slots - 596.03ms
