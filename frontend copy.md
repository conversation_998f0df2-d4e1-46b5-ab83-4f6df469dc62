flutter: Custom back button tapped
flutter: NavigatorService.goBack called
flutter: Can pop - proceeding with pop
flutter: Pop successful
flutter: NavigatorService.goBack() succeeded
flutter: NavigatorService.pushNamed called for route: /CoachPrograms
flutter: [ImageGenerator] Input is null or empty, returning ""
flutter: [ImageGenerator] Input is null or empty, returning ""
flutter: [ImageGenerator] Input: "/uploads/1752758884124-mainImage-682e257644f4dce64bfee3f6.jpg"
flutter: [ImageGenerator] Detected relative server path, converting to: "http://*************:3000/uploads/1752758884124-mainImage-682e257644f4dce64bfee3f6.jpg"
flutter: [ImageGenerator] Input: "/uploads/1753001437006-mainImage-682e257644f4dce64bfee3f6.jpg"
flutter: [ImageGenerator] Detected relative server path, converting to: "http://*************:3000/uploads/1753001437006-mainImage-682e257644f4dce64bfee3f6.jpg"
flutter: [ImageGenerator] Input: "/uploads/1753032836003-mainImage-682e257644f4dce64bfee3f6.jpg"
flutter: [ImageGenerator] Detected relative server path, converting to: "http://*************:3000/uploads/1753032836003-mainImage-682e257644f4dce64bfee3f6.jpg"
flutter: [ImageGenerator] Input: "/uploads/1753284246019-mainImage-682e257644f4dce64bfee3f6.png"
flutter: [ImageGenerator] Detected relative server path, converting to: "http://*************:3000/uploads/1753284246019-mainImage-682e257644f4dce64bfee3f6.png"
flutter: [ImageGenerator] Input: "/uploads/1753378324710-mainImage-682e257644f4dce64bfee3f6.jpg"
flutter: [ImageGenerator] Detected relative server path, converting to: "http://*************:3000/uploads/1753378324710-mainImage-682e257644f4dce64bfee3f6.jpg"
flutter: [ImageGenerator] Input: "/uploads/1753378741274-mainImage-682e257644f4dce64bfee3f6.jpg"
flutter: [ImageGenerator] Detected relative server path, converting to: "http://*************:3000/uploads/1753378741274-mainImage-682e257644f4dce64bfee3f6.jpg"
flutter: [ImageGenerator] Input: "/uploads/1753379279414-mainImage-682e257644f4dce64bfee3f6.jpg"
flutter: [ImageGenerator] Detected relative server path, converting to: "http://*************:3000/uploads/1753379279414-mainImage-682e257644f4dce64bfee3f6.jpg"
flutter: [ImageGenerator] Input: "/uploads/1753390257980-mainImage-682e257644f4dce64bfee3f6.jpg"
flutter: [ImageGenerator] Detected relative server path, converting to: "http://*************:3000/uploads/1753390257980-mainImage-682e257644f4dce64bfee3f6.jpg"
flutter: [ImageGenerator] Input: "/uploads/1753669847509-mainImage-682e257644f4dce64bfee3f6.jpg"
flutter: [ImageGenerator] Detected relative server path, converting to: "http://*************:3000/uploads/1753669847509-mainImage-682e257644f4dce64bfee3f6.jpg"
flutter: Request URI: http://*************:3000/api/class/center/682e257644f4dce64bfee3f6
flutter: Headers: {}
flutter: DEBUG COACH MAIN: Received state: CenterLoadingState
flutter: DEBUG COACH MAIN: Loading state
flutter: [loadingState] Showing dialog
flutter: Response status code: 200
flutter: Response body: [{"_id":"6877f55558313742b2099661","center":{"_id":"682e257644f4dce64bfee3f6","legalName":"Brianjjjggyg","displayName":"briangjkkk","address":{"coordinates":{"lat":null,"lng":null},"address1":"12345","address2":"https://www.google.com/maps/place/3%E6%A8%93+PicCollage+Taipei+Headquarters,+No.+102%E8%99%9F,+Guangfu+S+Rd,+Da%E2%80%99an+District,+Taipei+City,+106/@25.0433959,121.5574278,16z/data=!4m6!3m5!1s0x3442abc72e8f5899:0x9790e242924537b2!8m2!3d25.0433959!4d121.5574278!16s%2Fg%2F11c3k96q36?g_ep=Eg1tbF8yMDI1MDYxNl8wIJvbDyoASAJQAg%3D%3D","city":"taipei","region":"hongkong","country":"Hong Kong","_id":"68827c9f28459dc72456aaa1"},"companyNumber":""},"student":[],"category":"Language","classProviding":"Brian","level":"beginer","description":"good course","mode":false,"sen":false,"initialCharge":"0","charge":1,"ageFrom":11,"ageTo":12,"minimumStudent":1,"numberOfClass":0,"languageOptions":[],"status":"active","rearrangementInfo":{"isRearranging":false,"originalDates":[],"newDa<…>
flutter: 🔍 ClassModel.fromJson - Coach parsing:
flutter:   - Coach JSON type: _Map<String, dynamic>
flutter:   - Coach JSON value: {mainImage: {url: /uploads/67e7af5b22a791ee0bc40fd6-1743238539269.jpg, contentType: image/jpeg}, manager: null, legalName: , phone: , rating: 0, reviewCount: 0, programs: [], _id: 67e7af5b22a791ee0bc40fda, baseUser: 67e7af5b22a791ee0bc40fd6, email: <EMAIL>, displayName: Coach Ten, address: {coordinates: {lat: null, lng: null}, address1: , address2: , city: , region: , _id: 67e7b597d8c80c14a806c689, country: Hong Kong}, languages: [German], description: Cycling coach for competitive athletes, ageFrom: 15, ageTo: 25, sen: false, experience: [{title: Cycling Performance Coach, organization: National Cycling League, from: 2012, to: 2022, _id: 67e7b597d8c80c14a806c68a}], skill: [{skillname: Endurance Training, skillsince: 2008, skilllevel: Expert, skillnameofassociation: International Cycling Union, _id: 67e7b597d8c80c14a806c68b}], accredation: [{name: UCI Coaching License, result: Achieved, year: 2017, _id: 67e7b597d8c80c14a806c68c}], images: [], createdAt<…>
flutter:   - Result: Parsing full CoachModel from JSON
flutter: 🛠️ CoachModel.fromJson CALLED with: {mainImage: {url: /uploads/67e7af5b22a791ee0bc40fd6-1743238539269.jpg, contentType: image/jpeg}, manager: null, legalName: , phone: , rating: 0, reviewCount: 0, programs: [], _id: 67e7af5b22a791ee0bc40fda, baseUser: 67e7af5b22a791ee0bc40fd6, email: <EMAIL>, displayName: Coach Ten, address: {coordinates: {lat: null, lng: null}, address1: , address2: , city: , region: , _id: 67e7b597d8c80c14a806c689, country: Hong Kong}, languages: [German], description: Cycling coach for competitive athletes, ageFrom: 15, ageTo: 25, sen: false, experience: [{title: Cycling Performance Coach, organization: National Cycling League, from: 2012, to: 2022, _id: 67e7b597d8c80c14a806c68a}], skill: [{skillname: Endurance Training, skillsince: 2008, skilllevel: Expert, skillnameofassociation: International Cycling Union, _id: 67e7b597d8c80c14a806c68b}], accredation: [{name: UCI Coaching License, result: Achieved, year: 2017, _id: 67e7b597d8c80c14a806c68c}], im<…>
flutter:   - Parsed coach ID: 67e7af5b22a791ee0bc40fda
flutter:   - Parsed coach name: Coach Ten
flutter: 🔍 ClassModel.fromJson - Coach parsing:
flutter:   - Coach JSON type: _Map<String, dynamic>
flutter:   - Coach JSON value: {mainImage: {url: /uploads/67e7af5122a791ee0bc40fc1-1743238427218.jpg, contentType: image/jpeg}, _id: 67e7af5122a791ee0bc40fc5, baseUser: 67e7af5122a791ee0bc40fc1, email: <EMAIL>, displayName: Coach Seven, address: {coordinates: {lat: null, lng: null}, address1: , address2: , city: , region: , country: Hong Kong, _id: 67e7b52cd8c80c14a806c64d}, languages: [English, French], description: Expert in rugby coaching, ageFrom: 12, ageTo: 20, sen: false, experience: [{title: Rugby Head Coach, organization: National Rugby Academy, from: 2016, to: 2023, _id: 67e7b52cd8c80c14a806c64e}], skill: [{skillname: Tackling, skillsince: 2012, skilllevel: Elite, skillnameofassociation: World Rugby Association, _id: 67e7b52cd8c80c14a806c64f}], accredation: [{name: World Rugby Level 3, result: Completed, year: 2021, _id: 67e7b52cd8c80c14a806c650}], images: [], createdAt: 2025-03-29T08:29:05.491Z, updatedAt: 2025-06-24T10:40:31.103Z, __v: 0, center: 682e257644f4dce64bfee3f6, <…>
flutter:   - Result: Parsing full CoachModel from JSON
flutter: 🛠️ CoachModel.fromJson CALLED with: {mainImage: {url: /uploads/67e7af5122a791ee0bc40fc1-1743238427218.jpg, contentType: image/jpeg}, _id: 67e7af5122a791ee0bc40fc5, baseUser: 67e7af5122a791ee0bc40fc1, email: <EMAIL>, displayName: Coach Seven, address: {coordinates: {lat: null, lng: null}, address1: , address2: , city: , region: , country: Hong Kong, _id: 67e7b52cd8c80c14a806c64d}, languages: [English, French], description: Expert in rugby coaching, ageFrom: 12, ageTo: 20, sen: false, experience: [{title: Rugby Head Coach, organization: National Rugby Academy, from: 2016, to: 2023, _id: 67e7b52cd8c80c14a806c64e}], skill: [{skillname: Tackling, skillsince: 2012, skilllevel: Elite, skillnameofassociation: World Rugby Association, _id: 67e7b52cd8c80c14a806c64f}], accredation: [{name: World Rugby Level 3, result: Completed, year: 2021, _id: 67e7b52cd8c80c14a806c650}], images: [], createdAt: 2025-03-29T08:29:05.491Z, updatedAt: 2025-06-24T10:40:31.103Z, __v: 0, center: 682e257<…>
flutter:   - Parsed coach ID: 67e7af5122a791ee0bc40fc5
flutter:   - Parsed coach name: Coach Seven
flutter: 🔍 ClassModel.fromJson - Coach parsing:
flutter:   - Coach JSON type: _Map<String, dynamic>
flutter:   - Coach JSON value: {mainImage: {url: /uploads/1743242183494-671751c6a5265ddcb386e77c-mainImage.png, contentType: image/png}, _id: 6718a42e9643902397d094c8, baseUser: 671751c6a5265ddcb386e77c, displayName: john doe, address: {coordinates: {lat: null, lng: null}, address1: 123 Main St, address2: Apt 4B, city: New York, region: NY, country: Hong Kong, _id: 6718a42e9643902397d094c9}, languages: null, description: null, experience: [{_id: 6808bca2c44eb4ad5755c844}], skill: [{_id: 6808bca2c44eb4ad5755c845}], ageFrom: 25, ageTo: 40, sen: true, accredation: [{_id: 6808bca2c44eb4ad5755c846}], images: [], createdAt: 2024-10-23T07:22:22.148Z, updatedAt: 2025-07-23T14:17:57.947Z, __v: 0, email: <EMAIL>, programs: [671f81cb85afe092abc29674], rating: 5, reviewCount: 0, center: null, manager: 680aaa9ea7a8843160c47f13, legalName: , phone: , classzId: COA-250619-TZIO}
flutter:   - Result: Parsing full CoachModel from JSON
flutter: 🛠️ CoachModel.fromJson CALLED with: {mainImage: {url: /uploads/1743242183494-671751c6a5265ddcb386e77c-mainImage.png, contentType: image/png}, _id: 6718a42e9643902397d094c8, baseUser: 671751c6a5265ddcb386e77c, displayName: john doe, address: {coordinates: {lat: null, lng: null}, address1: 123 Main St, address2: Apt 4B, city: New York, region: NY, country: Hong Kong, _id: 6718a42e9643902397d094c9}, languages: null, description: null, experience: [{_id: 6808bca2c44eb4ad5755c844}], skill: [{_id: 6808bca2c44eb4ad5755c845}], ageFrom: 25, ageTo: 40, sen: true, accredation: [{_id: 6808bca2c44eb4ad5755c846}], images: [], createdAt: 2024-10-23T07:22:22.148Z, updatedAt: 2025-07-23T14:17:57.947Z, __v: 0, email: <EMAIL>, programs: [671f81cb85afe092abc29674], rating: 5, reviewCount: 0, center: null, manager: 680aaa9ea7a8843160c47f13, legalName: , phone: , classzId: COA-250619-TZIO}
flutter:   - Parsed coach ID: 6718a42e9643902397d094c8
flutter:   - Parsed coach name: john doe
flutter: 🔍 ClassModel.fromJson - Coach parsing:
flutter:   - Coach JSON type: _Map<String, dynamic>
flutter:   - Coach JSON value: {mainImage: {url: /uploads/mainImage-67e7af4322a791ee0bc40fa5.jpg, contentType: image/jpeg}, _id: 67e7af4322a791ee0bc40fa9, baseUser: 67e7af4322a791ee0bc40fa5, email: <EMAIL>, displayName: Coach Three, address: {coordinates: {lat: null, lng: null}, address1: , address2: , city: , region: , country: Hong Kong, _id: 67e7b47dd8c80c14a806c607}, languages: [German, English, Spanish], description: Certified swimmingxz coach, ageFrom: 5, ageTo: 15, sen: false, experience: [{title: Aquatics Director, organization: City Swimming Club, from: 2012, to: 2022, _id: 67e7b47dd8c80c14a806c608}], skill: [{skillname: Butterfly Stroke, skillsince: 2005, skilllevel: Professional, skillnameofassociation: International Swimming Federation, _id: 67e7b47dd8c80c14a806c609}], accredation: [{name: Lifeguard Training Certificate, result: Certified, year: 2015, _id: 67e7b47dd8c80c14a806c60a}], images: [{url: /home/<USER>/repositories/classZ_Backend/uploads/images-1750446256266-88<…>
flutter:   - Result: Parsing full CoachModel from JSON
flutter: 🛠️ CoachModel.fromJson CALLED with: {mainImage: {url: /uploads/mainImage-67e7af4322a791ee0bc40fa5.jpg, contentType: image/jpeg}, _id: 67e7af4322a791ee0bc40fa9, baseUser: 67e7af4322a791ee0bc40fa5, email: <EMAIL>, displayName: Coach Three, address: {coordinates: {lat: null, lng: null}, address1: , address2: , city: , region: , country: Hong Kong, _id: 67e7b47dd8c80c14a806c607}, languages: [German, English, Spanish], description: Certified swimmingxz coach, ageFrom: 5, ageTo: 15, sen: false, experience: [{title: Aquatics Director, organization: City Swimming Club, from: 2012, to: 2022, _id: 67e7b47dd8c80c14a806c608}], skill: [{skillname: Butterfly Stroke, skillsince: 2005, skilllevel: Professional, skillnameofassociation: International Swimming Federation, _id: 67e7b47dd8c80c14a806c609}], accredation: [{name: Lifeguard Training Certificate, result: Certified, year: 2015, _id: 67e7b47dd8c80c14a806c60a}], images: [{url: /home/<USER>/repositories/classZ_Backend/uploads/imag<…>
flutter:   - Parsed coach ID: 67e7af4322a791ee0bc40fa9
flutter:   - Parsed coach name: Coach Three
flutter: 🔍 ClassModel.fromJson - Coach parsing:
flutter:   - Coach JSON type: _Map<String, dynamic>
flutter:   - Coach JSON value: {mainImage: {url: /uploads/mainImage-67e7af4322a791ee0bc40fa5.jpg, contentType: image/jpeg}, _id: 67e7af4322a791ee0bc40fa9, baseUser: 67e7af4322a791ee0bc40fa5, email: <EMAIL>, displayName: Coach Three, address: {coordinates: {lat: null, lng: null}, address1: , address2: , city: , region: , country: Hong Kong, _id: 67e7b47dd8c80c14a806c607}, languages: [German, English, Spanish], description: Certified swimmingxz coach, ageFrom: 5, ageTo: 15, sen: false, experience: [{title: Aquatics Director, organization: City Swimming Club, from: 2012, to: 2022, _id: 67e7b47dd8c80c14a806c608}], skill: [{skillname: Butterfly Stroke, skillsince: 2005, skilllevel: Professional, skillnameofassociation: International Swimming Federation, _id: 67e7b47dd8c80c14a806c609}], accredation: [{name: Lifeguard Training Certificate, result: Certified, year: 2015, _id: 67e7b47dd8c80c14a806c60a}], images: [{url: /home/<USER>/repositories/classZ_Backend/uploads/images-1750446256266-88<…>
flutter:   - Result: Parsing full CoachModel from JSON
flutter: 🛠️ CoachModel.fromJson CALLED with: {mainImage: {url: /uploads/mainImage-67e7af4322a791ee0bc40fa5.jpg, contentType: image/jpeg}, _id: 67e7af4322a791ee0bc40fa9, baseUser: 67e7af4322a791ee0bc40fa5, email: <EMAIL>, displayName: Coach Three, address: {coordinates: {lat: null, lng: null}, address1: , address2: , city: , region: , country: Hong Kong, _id: 67e7b47dd8c80c14a806c607}, languages: [German, English, Spanish], description: Certified swimmingxz coach, ageFrom: 5, ageTo: 15, sen: false, experience: [{title: Aquatics Director, organization: City Swimming Club, from: 2012, to: 2022, _id: 67e7b47dd8c80c14a806c608}], skill: [{skillname: Butterfly Stroke, skillsince: 2005, skilllevel: Professional, skillnameofassociation: International Swimming Federation, _id: 67e7b47dd8c80c14a806c609}], accredation: [{name: Lifeguard Training Certificate, result: Certified, year: 2015, _id: 67e7b47dd8c80c14a806c60a}], images: [{url: /home/<USER>/repositories/classZ_Backend/uploads/imag<…>
flutter:   - Parsed coach ID: 67e7af4322a791ee0bc40fa9
flutter:   - Parsed coach name: Coach Three
flutter: 🔍 ClassModel.fromJson - Coach parsing:
flutter:   - Coach JSON type: _Map<String, dynamic>
flutter:   - Coach JSON value: {mainImage: {url: /uploads/mainImage-67e7af4322a791ee0bc40fa5.jpg, contentType: image/jpeg}, _id: 67e7af4322a791ee0bc40fa9, baseUser: 67e7af4322a791ee0bc40fa5, email: <EMAIL>, displayName: Coach Three, address: {coordinates: {lat: null, lng: null}, address1: , address2: , city: , region: , country: Hong Kong, _id: 67e7b47dd8c80c14a806c607}, languages: [German, English, Spanish], description: Certified swimmingxz coach, ageFrom: 5, ageTo: 15, sen: false, experience: [{title: Aquatics Director, organization: City Swimming Club, from: 2012, to: 2022, _id: 67e7b47dd8c80c14a806c608}], skill: [{skillname: Butterfly Stroke, skillsince: 2005, skilllevel: Professional, skillnameofassociation: International Swimming Federation, _id: 67e7b47dd8c80c14a806c609}], accredation: [{name: Lifeguard Training Certificate, result: Certified, year: 2015, _id: 67e7b47dd8c80c14a806c60a}], images: [{url: /home/<USER>/repositories/classZ_Backend/uploads/images-1750446256266-88<…>
flutter:   - Result: Parsing full CoachModel from JSON
flutter: 🛠️ CoachModel.fromJson CALLED with: {mainImage: {url: /uploads/mainImage-67e7af4322a791ee0bc40fa5.jpg, contentType: image/jpeg}, _id: 67e7af4322a791ee0bc40fa9, baseUser: 67e7af4322a791ee0bc40fa5, email: <EMAIL>, displayName: Coach Three, address: {coordinates: {lat: null, lng: null}, address1: , address2: , city: , region: , country: Hong Kong, _id: 67e7b47dd8c80c14a806c607}, languages: [German, English, Spanish], description: Certified swimmingxz coach, ageFrom: 5, ageTo: 15, sen: false, experience: [{title: Aquatics Director, organization: City Swimming Club, from: 2012, to: 2022, _id: 67e7b47dd8c80c14a806c608}], skill: [{skillname: Butterfly Stroke, skillsince: 2005, skilllevel: Professional, skillnameofassociation: International Swimming Federation, _id: 67e7b47dd8c80c14a806c609}], accredation: [{name: Lifeguard Training Certificate, result: Certified, year: 2015, _id: 67e7b47dd8c80c14a806c60a}], images: [{url: /home/<USER>/repositories/classZ_Backend/uploads/imag<…>
flutter:   - Parsed coach ID: 67e7af4322a791ee0bc40fa9
flutter:   - Parsed coach name: Coach Three
flutter: 🔍 ClassModel.fromJson - Coach parsing:
flutter:   - Coach JSON type: Null
flutter:   - Coach JSON value: null
flutter:   - Result: null (coach is null)
flutter: 🔍 ClassModel.fromJson - Coach parsing:
flutter:   - Coach JSON type: Null
flutter:   - Coach JSON value: null
flutter:   - Result: null (coach is null)
flutter: 🔍 ClassModel.fromJson - Coach parsing:
flutter:   - Coach JSON type: Null
flutter:   - Coach JSON value: null
flutter:   - Result: null (coach is null)
flutter: 🔍 ClassModel.fromJson - Coach parsing:
flutter:   - Coach JSON type: _Map<String, dynamic>
flutter:   - Coach JSON value: {mainImage: {url: /uploads/mainImage-67e7af4322a791ee0bc40fa5.jpg, contentType: image/jpeg}, _id: 67e7af4322a791ee0bc40fa9, baseUser: 67e7af4322a791ee0bc40fa5, email: <EMAIL>, displayName: Coach Three, address: {coordinates: {lat: null, lng: null}, address1: , address2: , city: , region: , country: Hong Kong, _id: 67e7b47dd8c80c14a806c607}, languages: [German, English, Spanish], description: Certified swimmingxz coach, ageFrom: 5, ageTo: 15, sen: false, experience: [{title: Aquatics Director, organization: City Swimming Club, from: 2012, to: 2022, _id: 67e7b47dd8c80c14a806c608}], skill: [{skillname: Butterfly Stroke, skillsince: 2005, skilllevel: Professional, skillnameofassociation: International Swimming Federation, _id: 67e7b47dd8c80c14a806c609}], accredation: [{name: Lifeguard Training Certificate, result: Certified, year: 2015, _id: 67e7b47dd8c80c14a806c60a}], images: [{url: /home/<USER>/repositories/classZ_Backend/uploads/images-1750446256266-88<…>
flutter:   - Result: Parsing full CoachModel from JSON
flutter: 🛠️ CoachModel.fromJson CALLED with: {mainImage: {url: /uploads/mainImage-67e7af4322a791ee0bc40fa5.jpg, contentType: image/jpeg}, _id: 67e7af4322a791ee0bc40fa9, baseUser: 67e7af4322a791ee0bc40fa5, email: <EMAIL>, displayName: Coach Three, address: {coordinates: {lat: null, lng: null}, address1: , address2: , city: , region: , country: Hong Kong, _id: 67e7b47dd8c80c14a806c607}, languages: [German, English, Spanish], description: Certified swimmingxz coach, ageFrom: 5, ageTo: 15, sen: false, experience: [{title: Aquatics Director, organization: City Swimming Club, from: 2012, to: 2022, _id: 67e7b47dd8c80c14a806c608}], skill: [{skillname: Butterfly Stroke, skillsince: 2005, skilllevel: Professional, skillnameofassociation: International Swimming Federation, _id: 67e7b47dd8c80c14a806c609}], accredation: [{name: Lifeguard Training Certificate, result: Certified, year: 2015, _id: 67e7b47dd8c80c14a806c60a}], images: [{url: /home/<USER>/repositories/classZ_Backend/uploads/imag<…>
flutter:   - Parsed coach ID: 67e7af4322a791ee0bc40fa9
flutter:   - Parsed coach name: Coach Three
flutter: 🔍 ClassModel.fromJson - Coach parsing:
flutter:   - Coach JSON type: _Map<String, dynamic>
flutter:   - Coach JSON value: {mainImage: {url: /uploads/mainImage-67e7af4322a791ee0bc40fa5.jpg, contentType: image/jpeg}, _id: 67e7af4322a791ee0bc40fa9, baseUser: 67e7af4322a791ee0bc40fa5, email: <EMAIL>, displayName: Coach Three, address: {coordinates: {lat: null, lng: null}, address1: , address2: , city: , region: , country: Hong Kong, _id: 67e7b47dd8c80c14a806c607}, languages: [German, English, Spanish], description: Certified swimmingxz coach, ageFrom: 5, ageTo: 15, sen: false, experience: [{title: Aquatics Director, organization: City Swimming Club, from: 2012, to: 2022, _id: 67e7b47dd8c80c14a806c608}], skill: [{skillname: Butterfly Stroke, skillsince: 2005, skilllevel: Professional, skillnameofassociation: International Swimming Federation, _id: 67e7b47dd8c80c14a806c609}], accredation: [{name: Lifeguard Training Certificate, result: Certified, year: 2015, _id: 67e7b47dd8c80c14a806c60a}], images: [{url: /home/<USER>/repositories/classZ_Backend/uploads/images-1750446256266-88<…>
flutter:   - Result: Parsing full CoachModel from JSON
flutter: 🛠️ CoachModel.fromJson CALLED with: {mainImage: {url: /uploads/mainImage-67e7af4322a791ee0bc40fa5.jpg, contentType: image/jpeg}, _id: 67e7af4322a791ee0bc40fa9, baseUser: 67e7af4322a791ee0bc40fa5, email: <EMAIL>, displayName: Coach Three, address: {coordinates: {lat: null, lng: null}, address1: , address2: , city: , region: , country: Hong Kong, _id: 67e7b47dd8c80c14a806c607}, languages: [German, English, Spanish], description: Certified swimmingxz coach, ageFrom: 5, ageTo: 15, sen: false, experience: [{title: Aquatics Director, organization: City Swimming Club, from: 2012, to: 2022, _id: 67e7b47dd8c80c14a806c608}], skill: [{skillname: Butterfly Stroke, skillsince: 2005, skilllevel: Professional, skillnameofassociation: International Swimming Federation, _id: 67e7b47dd8c80c14a806c609}], accredation: [{name: Lifeguard Training Certificate, result: Certified, year: 2015, _id: 67e7b47dd8c80c14a806c60a}], images: [{url: /home/<USER>/repositories/classZ_Backend/uploads/imag<…>
flutter:   - Parsed coach ID: 67e7af4322a791ee0bc40fa9
flutter:   - Parsed coach name: Coach Three
flutter: 🔍 Updated class test for slot charge charge from schedule: 2
flutter: 🔍 Updated class new change for class charge from schedule: 2
flutter: 🔍 Updated class 1234 charge from schedule: 1
flutter: 🔍 Updated class coding ai charge from schedule: 400
flutter: 🔍 Updated class test with initial charge charge from schedule: 15
flutter: 🔍 Updated class 12 charge from schedule: 60
flutter: DEBUG COACH MAIN: Received state: ClassListFetchSuccess
flutter: [hideLoadingDialog] Called
flutter:   - Showing: true
flutter:   - Lock: false
flutter: [hideLoadingDialog] Dialog dismissed
flutter: [loadingState] Dialog closed
flutter: [ImageGenerator] Input is null or empty, returning ""
flutter: [ImageGenerator] Input is null or empty, returning ""
flutter: [ImageGenerator] Input: "/uploads/1752758884124-mainImage-682e257644f4dce64bfee3f6.jpg"
flutter: [ImageGenerator] Detected relative server path, converting to: "http://*************:3000/uploads/1752758884124-mainImage-682e257644f4dce64bfee3f6.jpg"
flutter: [ImageGenerator] Input: "/uploads/1753001437006-mainImage-682e257644f4dce64bfee3f6.jpg"
flutter: [ImageGenerator] Detected relative server path, converting to: "http://*************:3000/uploads/1753001437006-mainImage-682e257644f4dce64bfee3f6.jpg"
flutter: [ImageGenerator] Input: "/uploads/1753032836003-mainImage-682e257644f4dce64bfee3f6.jpg"
flutter: [ImageGenerator] Detected relative server path, converting to: "http://*************:3000/uploads/1753032836003-mainImage-682e257644f4dce64bfee3f6.jpg"
flutter: [ImageGenerator] Input: "/uploads/1753284246019-mainImage-682e257644f4dce64bfee3f6.png"
flutter: [ImageGenerator] Detected relative server path, converting to: "http://*************:3000/uploads/1753284246019-mainImage-682e257644f4dce64bfee3f6.png"
flutter: [ImageGenerator] Input: "/uploads/1753378324710-mainImage-682e257644f4dce64bfee3f6.jpg"
flutter: [ImageGenerator] Detected relative server path, converting to: "http://*************:3000/uploads/1753378324710-mainImage-682e257644f4dce64bfee3f6.jpg"
flutter: [ImageGenerator] Input: "/uploads/1753378741274-mainImage-682e257644f4dce64bfee3f6.jpg"
flutter: [ImageGenerator] Detected relative server path, converting to: "http://*************:3000/uploads/1753378741274-mainImage-682e257644f4dce64bfee3f6.jpg"
flutter: [ImageGenerator] Input: "/uploads/1753379279414-mainImage-682e257644f4dce64bfee3f6.jpg"
flutter: [ImageGenerator] Detected relative server path, converting to: "http://*************:3000/uploads/1753379279414-mainImage-682e257644f4dce64bfee3f6.jpg"
flutter: [ImageGenerator] Input: "/uploads/1753390257980-mainImage-682e257644f4dce64bfee3f6.jpg"
flutter: [ImageGenerator] Detected relative server path, converting to: "http://*************:3000/uploads/1753390257980-mainImage-682e257644f4dce64bfee3f6.jpg"
flutter: [ImageGenerator] Input: "/uploads/1753669847509-mainImage-682e257644f4dce64bfee3f6.jpg"
flutter: [ImageGenerator] Detected relative server path, converting to: "http://*************:3000/uploads/1753669847509-mainImage-682e257644f4dce64bfee3f6.jpg"
