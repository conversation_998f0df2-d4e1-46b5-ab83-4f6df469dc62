import 'package:class_z/core/imports.dart';

import 'package:dio/dio.dart' as dio;

import 'package:http/http.dart' as http;
import 'package:path/path.dart' as path;
import 'package:class_z/core/common/data/models/classDate_model.dart';

abstract class CenterDataSource {
  Future<bool> CreateBranchCenter(
      {required Map<String, dynamic> data,
      File? mainImage,
      File? businessCertificate,
      File? hkidCard,
      List<File>? images});
  Future<CenterData> centerInfoComplete();
  Future<CenterData> getCenter(String id);
  Future<List<CenterData>> getAllCenter(
      {required int page, required int limit});
  Future<List<CoachModel>> getCoachsByCenterId({required String id});
  Future<List<CoachModel>> getManagersByCenterId({required String id});
  Future<CenterData> updateCenter(
      {required String centerId, required Map<String, dynamic> data});

  Future<List<ClassModel>> getAllClassByCenterId(String centerId);
  Future<List<ClassModel>> getAllClassByCoachId(String coachId);
  Future<List<ClassModel>> getAllClassesForParent({required String centerId});
  Future<String?> addClass(
      {required String classProviding,
      required String category,
      String? level,
      required String description,
      required bool mode,
      bool? sen,
      String? initialCharge,
      required String charge,
      required String from,
      required String to,
      File? image});
  Future<bool> deleteClass({required String id});
  Future<bool> updateClass(
      {required String classId, required Map<String, dynamic> updatedData});

  Future<List<EventModel>> getAllEvents(
      {required String filterType, required String filterValue, String? date});
  Future<List<String>> getEventDates(
      {required String filterType, required String filterValue});
  Future<EventDatesModel> getEventsByClassId({required String classId});
  Future<EventModel> getEventsByEventId({required String eventId});
  Future<bool> deleteEventsbyEventId({required String eventId});
  Future<List<EventModel>> getEventsByDate({required String id});
  Future<AttendanceModel> postAttendance(
      {String? classId, String? code, String? qrCodeData, String? classDate});
  Future<List<AttendanceModel>> getPresentAttendance(
      {required String classId, required String classDate});
  Future<List<PendingModel>> getPendingReview({required String id});
  Future<List<PendingModel>> getPendingReviewByClassId(
      {required String classId});
  Future<List<PendingModel>> getPendingReviewByCoachId(
      {required String coachId});
  Future<List<ChildModel>> getStudentsByClassId({required String classId});
  Future<ClassModel> getCoachAndCenterByClassId({required String classId});
  Future<bool> classSlotDelete({
    required String classId,
    String? cancelType,
    String? eventId,
    String? newDate,
    String? newStartTime,
    String? newEndTime,
  });
  Future<List<String>> getRequestSendedByCenterToCoach(
      {required String centerId, required String type});
  Future<bool> updateCoachInClass({
    required String classId,
    required String coachId,
  });
  Future<List<ClassDate>> getAllSlotsByClassId(String classId);
  Future<bool> deleteSlotById(String slotId);
  Future<bool> deleteEventById(String eventId);
}

class CenterDataSourceImpl extends CenterDataSource {
  final http.Client client;
  final dio.Dio dioClient;
  final SharedRepository sharedRepository;
  final String device = AppText.device;
  final ApiService apiService;
  CenterDataSourceImpl(
      {required this.client,
      required this.dioClient,
      required this.sharedRepository,
      required this.apiService});
  @override
  Future<CenterData> centerInfoComplete() {
    throw UnimplementedError();
  }

  Future<bool> CreateBranchCenter({
    required Map<String, dynamic> data,
    File? mainImage,
    File? businessCertificate,
    File? hkidCard,
    List<File>? images,
  }) async {
    try {
      String url = '$device/api/center/create';
      String token = sharedRepository.getToken() ?? '';
      if (token.isEmpty) {
        print('Error: Token is empty');
        return false;
      }

      // Create a new FormData instance
      final formData = dio.FormData();

      // Log what we're about to send
      print("Creating center with the following fields:");

      // Add text fields (non-file fields)
      if (data.containsKey('baseUser')) {
        print("Adding baseUser: ${data['baseUser']}");
        formData.fields.add(MapEntry('baseUser', data['baseUser'].toString()));
      }

      if (data.containsKey('owner')) {
        print("Adding owner: ${data['owner']}");
        formData.fields.add(MapEntry('owner', data['owner'].toString()));
      }

      if (data.containsKey('legalName')) {
        print("Adding legalName: ${data['legalName']}");
        formData.fields
            .add(MapEntry('legalName', data['legalName'].toString()));
      }

      if (data.containsKey('displayName')) {
        print("Adding displayName: ${data['displayName']}");
        formData.fields
            .add(MapEntry('displayName', data['displayName'].toString()));
      }

      if (data.containsKey('companyNumber')) {
        print("Adding companyNumber: ${data['companyNumber']}");
        formData.fields
            .add(MapEntry('companyNumber', data['companyNumber'].toString()));
      }

      if (data.containsKey('centerNumber')) {
        print("Adding centerNumber: ${data['centerNumber']}");
        formData.fields
            .add(MapEntry('centerNumber', data['centerNumber'].toString()));
      }

      if (data.containsKey('email')) {
        print("Adding email: ${data['email']}");
        formData.fields.add(MapEntry('email', data['email'].toString()));
      }

      // Handle address fields
      if (data.containsKey('address') && data['address'] is Map) {
        Map<String, dynamic> address = data['address'];
        if (address.containsKey('address1')) {
          print("Adding address.address1: ${address['address1']}");
          formData.fields.add(
              MapEntry('address.address1', address['address1'].toString()));
        }
        if (address.containsKey('address2')) {
          print("Adding address.address2: ${address['address2']}");
          formData.fields.add(
              MapEntry('address.address2', address['address2'].toString()));
        }
        if (address.containsKey('city')) {
          print("Adding address.city: ${address['city']}");
          formData.fields
              .add(MapEntry('address.city', address['city'].toString()));
        }
        if (address.containsKey('region')) {
          print("Adding address.region: ${address['region']}");
          formData.fields
              .add(MapEntry('address.region', address['region'].toString()));
        }
      }

      // Handle bank details
      if (data.containsKey('bankDetails') && data['bankDetails'] is Map) {
        Map<String, dynamic> bankDetails = data['bankDetails'];
        if (bankDetails.containsKey('bankName')) {
          print("Adding bankDetails.bankName: ${bankDetails['bankName']}");
          formData.fields.add(MapEntry(
              'bankDetails.bankName', bankDetails['bankName'].toString()));
        }
        if (bankDetails.containsKey('accountHolderName')) {
          print(
              "Adding bankDetails.accountHolderName: ${bankDetails['accountHolderName']}");
          formData.fields.add(MapEntry('bankDetails.accountHolderName',
              bankDetails['accountHolderName'].toString()));
        }
        if (bankDetails.containsKey('bankCode')) {
          print("Adding bankDetails.bankCode: ${bankDetails['bankCode']}");
          formData.fields.add(MapEntry(
              'bankDetails.bankCode', bankDetails['bankCode'].toString()));
        }
        if (bankDetails.containsKey('branchCode')) {
          print("Adding bankDetails.branchCode: ${bankDetails['branchCode']}");
          formData.fields.add(MapEntry(
              'bankDetails.branchCode', bankDetails['branchCode'].toString()));
        }
        if (bankDetails.containsKey('accountNumber')) {
          print(
              "Adding bankDetails.accountNumber: ${bankDetails['accountNumber']}");
          formData.fields.add(MapEntry('bankDetails.accountNumber',
              bankDetails['accountNumber'].toString()));
        }
      }
      // Description
      if (data.containsKey('description')) {
        print("Adding description: ${data['description']}");
        formData.fields
            .add(MapEntry('description', data['description'].toString()));
      }

// Languages
      if (data.containsKey('languages')) {
        List<dynamic> languages = data['languages'];
        for (var language in languages) {
          formData.fields.add(MapEntry('languages', language.toString()));
        }
      }

// Services
      if (data.containsKey('services')) {
        List<dynamic> services = data['services'];
        for (var service in services) {
          formData.fields.add(MapEntry('services', service.toString()));
        }
      }

// Opening Hours
      if (data.containsKey('openingHour')) {
        List<dynamic> openingHours = data['openingHour'];
        for (int i = 0; i < openingHours.length; i++) {
          final item = openingHours[i];
          formData.fields.add(MapEntry('openingHour[$i][day]', item['day']));
          formData.fields.add(
              MapEntry('openingHour[$i][openingTime]', item['openingTime']));
          formData.fields.add(
              MapEntry('openingHour[$i][closingTime]', item['closingTime']));
        }
      }

      // Handle main image
      if (data.containsKey('mainImage') && data['mainImage'] is File) {
        File file = data['mainImage'];
        if (isImageFile(file.path)) {
          print("Adding mainImage file: ${file.path}");
          formData.files.add(
            MapEntry(
              'mainImage',
              createImageMultipartFile(file),
            ),
          );
        } else {
          print(
              "WARNING: Skipping mainImage file as it's not a valid image: ${file.path}");
        }
      }

      // Handle business certificate
      if (data.containsKey('businessCertificate')) {
        if (data['businessCertificate'] is List<File>) {
          List<File> files = data['businessCertificate'];
          for (var file in files) {
            if (isImageFile(file.path)) {
              print("Adding businessCertificate file: ${file.path}");
              formData.files.add(
                MapEntry(
                  'businessCertificate', // Use singular form as per server expectation
                  createImageMultipartFile(file),
                ),
              );
            } else {
              print(
                  "WARNING: Skipping businessCertificate file as it's not a valid image: ${file.path}");
            }
          }
        } else if (data['businessCertificate'] is File) {
          File file = data['businessCertificate'];
          if (isImageFile(file.path)) {
            print("Adding businessCertificate file: ${file.path}");
            formData.files.add(
              MapEntry(
                'businessCertificate', // Use singular form as per server expectation
                createImageMultipartFile(file),
              ),
            );
          } else {
            print(
                "WARNING: Skipping businessCertificate file as it's not a valid image: ${file.path}");
          }
        }
      }

      // Handle sexual conviction record
      if (data.containsKey('sexualConvictionRecord')) {
        if (data['sexualConvictionRecord'] is List<File>) {
          List<File> files = data['sexualConvictionRecord'];
          for (var file in files) {
            if (isImageFile(file.path)) {
              print("Adding sexualConvictionRecord file: ${file.path}");
              formData.files.add(
                MapEntry(
                  'sexualConvictionRecord', // Use singular form as per server expectation
                  createImageMultipartFile(file),
                ),
              );
            } else {
              print(
                  "WARNING: Skipping sexualConvictionRecord file as it's not a valid image: ${file.path}");
            }
          }
        } else if (data['sexualConvictionRecord'] is File) {
          File file = data['sexualConvictionRecord'];
          if (isImageFile(file.path)) {
            print("Adding sexualConvictionRecord file: ${file.path}");
            formData.files.add(
              MapEntry(
                'sexualConvictionRecord', // Use singular form as per server expectation
                createImageMultipartFile(file),
              ),
            );
          } else {
            print(
                "WARNING: Skipping sexualConvictionRecord file as it's not a valid image: ${file.path}");
          }
        }
      }

      // Handle HKID card
      if (data.containsKey('hkidCard')) {
        if (data['hkidCard'] is List<File>) {
          List<File> files = data['hkidCard'];
          for (var file in files) {
            if (isImageFile(file.path)) {
              print("Adding hkidCard file: ${file.path}");
              formData.files.add(
                MapEntry(
                  'hkidCard', // Use singular form as per server expectation
                  createImageMultipartFile(file),
                ),
              );
            } else {
              print(
                  "WARNING: Skipping hkidCard file as it's not a valid image: ${file.path}");
            }
          }
        } else if (data['hkidCard'] is File) {
          File file = data['hkidCard'];
          if (isImageFile(file.path)) {
            print("Adding hkidCard file: ${file.path}");
            formData.files.add(
              MapEntry(
                'hkidCard', // Use singular form as per server expectation
                createImageMultipartFile(file),
              ),
            );
          } else {
            print(
                "WARNING: Skipping hkidCard file as it's not a valid image: ${file.path}");
          }
        }
      }

      // Handle center images
      if (data.containsKey('images')) {
        if (data['images'] is List<File>) {
          List<File> files = data['images'];
          for (var file in files) {
            if (isImageFile(file.path)) {
              print("Adding images file: ${file.path}");
              formData.files.add(
                MapEntry(
                  'images',
                  createImageMultipartFile(file),
                ),
              );
            } else {
              print(
                  "WARNING: Skipping images file as it's not a valid image: ${file.path}");
            }
          }
        } else if (data['images'] is File) {
          File file = data['images'];
          if (isImageFile(file.path)) {
            print("Adding images file: ${file.path}");
            formData.files.add(
              MapEntry(
                'images',
                createImageMultipartFile(file),
              ),
            );
          } else {
            print(
                "WARNING: Skipping images file as it's not a valid image: ${file.path}");
          }
        }
      }

      print("FormData fields count: ${formData.fields.length}");
      print("FormData files count: ${formData.files.length}");

      // Send the request
      var response = await dioClient.post(
        url,
        data: formData,
        options: dio.Options(
          headers: {
            'auth-token': token,
            // Let Dio set the Content-Type header automatically for multipart/form-data
          },
          receiveTimeout: const Duration(minutes: 2),
          sendTimeout: const Duration(minutes: 2),
        ),
        onSendProgress: (sent, total) {
          if (total > 0) {
            print(
                'Upload progress: ${(sent / total * 100).toStringAsFixed(0)}%');
          }
        },
      );

      // Check response
      if (response.statusCode! >= 200 && response.statusCode! <= 300) {
        print('Branch center created successfully: ${response.data}');
        return true; // Success
      } else {
        print(
            'Failed to create branch center. Status Code: ${response.statusCode}, Response: ${response.data}');
        return false; // Failure
      }
    } catch (e) {
      if (e is dio.DioException) {
        print(
            'Dio error while creating branch center: ${e.response?.data ?? e.message}');
        if (e.response != null) {
          print('Status code: ${e.response?.statusCode}');
          print('Response data: ${e.response?.data}');
        }
        throw Exception(
            'Failed to create branch center: ${e.response?.data ?? e.message}');
      } else {
        print('Error while creating branch center: $e');
      }
      return false;
    }
  }

  @override
  Future<CenterData> getCenter(String id) async {
    try {
      print("Fetching center data for ID: $id");

      // Get authentication token
      String? token = sharedRepository.getToken();
      if (token == null || token.isEmpty) {
        print(
            "Warning: No auth token available, proceeding without authentication");
      }

      String uri = '$device/api/auth/getcenter/$id';
      print("Making request to: $uri");

      final response = await client.get(
        Uri.parse(uri),
        headers: {
          'Content-Type': 'application/json',
          if (token != null && token.isNotEmpty) 'auth-token': token,
        },
      );

      print("Response status: ${response.statusCode}");
      print("Response body: ${response.body}");

      if (response.statusCode >= 200 && response.statusCode < 300) {
        final jsonData = json.decode(response.body);
        CenterData center = CenterData.fromJson(jsonData);
        print("Successfully parsed center data");
        return center;
      } else {
        print(
            "API error - Status: ${response.statusCode}, Body: ${response.body}");
        throw Exception(
            'Failed to fetch center data. Status: ${response.statusCode}');
      }
    } catch (e) {
      print("Error in getCenter: $e");
      throw Exception('Failed to fetch center data: ${e.toString()}');
    }
  }

  Future<List<CenterData>> getAllCenter({int page = 1, int limit = 10}) async {
    // Get the auth token for user identification
    String? token = sharedRepository.getToken();

    // Define the API endpoint with pagination query params
    String uri = "$device/api/auth/center?page=$page&limit=$limit";

    var response = await http.get(Uri.parse(uri), headers: {
      'Content-Type': 'application/json',
      'auth-token': token ?? "", // Include auth token for role-based filtering
    });

    if (response.statusCode >= 200 && response.statusCode <= 300) {
      final Map<String, dynamic> responseBody = jsonDecode(response.body);

      // Check if response contains 'data' key
      if (responseBody.containsKey('data')) {
        final List<dynamic> jsonData = responseBody['data'];

        List<CenterData> centers = jsonData.map((data) {
          return CenterData.fromJson(data);
        }).toList();

        return centers;
      } else {
        throw Exception('Invalid response format');
      }
    } else {
      print('Failed to load centers. Status code: ${response.statusCode}');
      throw Exception('Failed to load centers');
    }
  }

  @override
  Future<List<CoachModel>> getCoachsByCenterId({required String id}) async {
    try {
      if (id.isEmpty) {
        UserModel? user = sharedRepository.getUserData();
        id = user!.data!.center!.id!;
      }
      String? token = sharedRepository.getToken();
      print("Token: $token");

      // Define the API endpoint
      String uri = "$device/api/center/getCoach/$id";
      print(uri);
      var response = await http.get(
        Uri.parse(uri),
        headers: {
          'Content-Type': 'application/json',
          'auth-token': token ?? ""
        },
      );
      if (response.statusCode >= 200 && response.statusCode <= 300) {
        final List<dynamic> jsonData = jsonDecode(response.body);
        List<CoachModel> coachs = jsonData.map((data) {
          return CoachModel.fromJson(data);
        }).toList();
        return coachs;
      } else
        throw Exception('Please try again');
    } catch (e) {
      throw Exception('Failed to Fetch Coachs: ${e.toString()}');
    }
  }

  @override
  Future<List<CoachModel>> getManagersByCenterId({required String id}) async {
    try {
      if (id.isEmpty) {
        UserModel? user = sharedRepository.getUserData();
        id = user!.data!.center!.id!;
      }
      String? token = sharedRepository.getToken();
      print("Token: $token");

      // Define the API endpoint
      String uri = "$device/api/center/getManager/$id";
      print(uri);
      var response = await http.get(
        Uri.parse(uri),
        headers: {
          'Content-Type': 'application/json',
          'auth-token': token ?? ""
        },
      );
      if (response.statusCode >= 200 && response.statusCode <= 300) {
        final List<dynamic> jsonData = jsonDecode(response.body);
        List<CoachModel> coachs = jsonData.map((data) {
          return CoachModel.fromJson(data);
        }).toList();
        return coachs;
      } else
        throw Exception('Please try again');
    } catch (e) {
      throw Exception('Failed to Fetch Coachs: ${e.toString()}');
    }
  }

  @override
  Future<CenterData> updateCenter({
    required String centerId,
    required Map<String, dynamic> data,
  }) async {
    try {
      // Check for token first
      String? token = locator<SharedRepository>().getToken();
      if (token == null || token.isEmpty) {
        print("Error: Auth token is null or empty");
        throw Exception("Authentication token is missing");
      }

      String url = '$device/api/center/$centerId';
      print("Updating center at URL: $url");
      print("Data to update: ${data.keys}");

      // Create FormData for all requests to handle both files and regular data
      final formData = dio.FormData();

      // Create a copy of data to avoid modifying the original
      Map<String, dynamic> formFields = Map<String, dynamic>.from(data);

      // Process files in the data
      List<String> fileKeys = [];

      // Handle mainImage if present
      if (formFields.containsKey('mainImage') &&
          formFields['mainImage'] is File) {
        final File imageFile = formFields['mainImage'];
        print("Processing mainImage: ${imageFile.path}");

        if (imageFile.existsSync()) {
          formData.files.add(
            MapEntry(
              'mainImage',
              dio.MultipartFile.fromFileSync(
                imageFile.path,
                filename: path.basename(imageFile.path),
                contentType: dio.DioMediaType.parse(
                    sharedRepository.getContentType(imageFile.path).toString()),
              ),
            ),
          );
          fileKeys.add('mainImage');
        } else {
          print("Warning: mainImage file doesn't exist: ${imageFile.path}");
        }
      }

      // Handle images array if present
      if (formFields.containsKey('images') && formFields['images'] is List) {
        final List imagesList = formFields['images'];
        print("Processing ${imagesList.length} images");

        for (int i = 0; i < imagesList.length; i++) {
          if (imagesList[i] is File) {
            final File imageFile = imagesList[i];

            if (imageFile.existsSync()) {
              formData.files.add(
                MapEntry(
                  'images', // Use the same field name for all images
                  dio.MultipartFile.fromFileSync(
                    imageFile.path,
                    filename: path.basename(imageFile.path),
                    contentType: dio.DioMediaType.parse(sharedRepository
                        .getContentType(imageFile.path)
                        .toString()),
                  ),
                ),
              );
            } else {
              print(
                  "Warning: image file ${i + 1} doesn't exist: ${imageFile.path}");
            }
          }
        }
        fileKeys.add('images');
      }

      // Remove file fields from the data map since they're handled separately
      for (String key in fileKeys) {
        formFields.remove(key);
      }

      // Process address separately if it exists
      if (formFields.containsKey('address') && formFields['address'] is Map) {
        final addressMap = formFields['address'] as Map<String, dynamic>;

        // Add address fields individually to form data
        addressMap.forEach((subKey, subValue) {
          if (subValue != null) {
            formData.fields
                .add(MapEntry('address[$subKey]', subValue.toString()));
          }
        });

        // Remove address from data since we've processed it
        formFields.remove('address');
      }

      // Process arrays like services and languages
      for (String key in ['services', 'languages', 'openingHour']) {
        if (formFields.containsKey(key) && formFields[key] is List) {
          final List valueList = formFields[key];

          // If it's a simple list of strings
          if (valueList.isNotEmpty && valueList.first is String) {
            for (var value in valueList) {
              formData.fields.add(MapEntry('$key[]', value.toString()));
            }
          }
          // If it's a list of maps (like openingHour)
          else if (valueList.isNotEmpty && valueList.first is Map) {
            for (int i = 0; i < valueList.length; i++) {
              Map<String, dynamic> item = valueList[i];
              item.forEach((subKey, subValue) {
                formData.fields
                    .add(MapEntry('$key[$i][$subKey]', subValue.toString()));
              });
            }
          }

          formFields.remove(key);
        }
      }

      // Add all remaining fields to form data
      formFields.forEach((key, value) {
        if (value != null) {
          if (value is Map) {
            value.forEach((subKey, subValue) {
              if (subValue != null) {
                formData.fields
                    .add(MapEntry('$key[$subKey]', subValue.toString()));
              }
            });
          } else {
            formData.fields.add(MapEntry(key, value.toString()));
          }
        }
      });

      print(
          "FormData created with ${formData.files.length} files and ${formData.fields.length} fields");

      // Print all field names for debugging
      print("FormData fields: ${formData.fields.map((f) => f.key).toList()}");
      print("FormData files: ${formData.files.map((f) => f.key).toList()}");

      // Send the request with improved options
      var response = await dioClient.put(
        url,
        data: formData,
        options: dio.Options(
          headers: {
            'auth-token': token,
            'Content-Type': 'multipart/form-data',
          },
          receiveTimeout: const Duration(seconds: 30),
          sendTimeout: const Duration(seconds: 30),
        ),
        onSendProgress: (sent, total) {
          print('Upload progress: ${(sent / total * 100).toStringAsFixed(0)}%');
        },
      );

      print("Response status code: ${response.statusCode}");

      // Check if the response status is successful
      if (response.statusCode! >= 200 && response.statusCode! <= 300) {
        dynamic jsonData = response.data;
        print("Response data received: ${jsonData != null}");

        // Parse the response into CenterData
        CenterData updatedCenter = CenterData.fromJson(jsonData);
        print("Center data parsed successfully");

        // Check if the updated center is the same as the current one
        if (locator<SharedRepository>().getCenterId() == updatedCenter.id) {
          locator<SharedRepository>().updateCenterData(updatedCenter);
          print("Center data updated in shared repository");
        }

        return updatedCenter;
      } else {
        print("Error response: ${response.statusCode} - ${response.data}");
        throw Exception(
            'Failed to update center: Status ${response.statusCode}');
      }
    } catch (e) {
      if (e is dio.DioException) {
        print(
            'Dio error while updating center: ${e.response?.data ?? e.message}');
        if (e.response?.statusCode == 400) {
          print('Bad request details: ${e.response?.data}');
        }
      } else {
        print('Error updating center: $e');
      }
      throw Exception('Failed to update center: $e');
    }
  }

  @override
  Future<String?> addClass({
    required String classProviding,
    required String category,
    String? level,
    required String description,
    required bool mode,
    bool? sen,
    String? initialCharge,
    required String charge,
    required String from,
    required String to,
    File? image,
  }) async {
    try {
      print(image);
      UserModel? user = sharedRepository.getUserData();
      String centerId = user!.data!.center!.id!;
      String uri = "$device/api/class/addClass";
      print(centerId);
      // Create the payload with only non-null/empty values
      Map<String, String> payload = {
        "classProviding": classProviding,
        "description": description,
        "category": category,
        "mode": mode.toString(),
        "center": centerId,
        "charge": charge,
        "ageFrom": from,
        "ageTo": to
      };

      // Add optional fields if they are not null or empty
      if (level != null && level.isNotEmpty) {
        payload["level"] = level;
      }
      if (sen != null) {
        payload["sen"] = sen.toString();
      }
      if (initialCharge != null && initialCharge.isNotEmpty) {
        payload["initialCharge"] = initialCharge;
      }

      // Prepare the HTTP request
      var request = http.MultipartRequest('POST', Uri.parse(uri));

      // Add the payload data
      request.fields.addAll(payload);
      print(request.fields);
      // Handle image upload if it is provided
      if (image != null && await image.exists()) {
        var fileStream = http.ByteStream(image.openRead());
        var length = await image.length();
        var contentType = sharedRepository.getContentType(
            image.path); // Ensure this returns the correct content type

        var multipartFile = http.MultipartFile(
          'mainImage', // Change this key to match your API's expected field name
          fileStream,
          length,
          filename: path.basename(image.path),
          contentType: contentType, // Ensure content type is correctly parsed
        );

        request.files.add(multipartFile);
      }
      print(request.files
          .toString()); // This will print basic info about the files added to the request.

      // Perform the HTTP request
      var response = await request.send();

      // Process the response
      if (response.statusCode >= 200 && response.statusCode <= 300) {
        // Parse the response to get the class ID
        String responseBody = await response.stream.bytesToString();
        print('🔍 DEBUG: addClass response body: $responseBody');

        try {
          Map<String, dynamic> responseJson = json.decode(responseBody);
          // Backend returns { token, classs } - extract the class ID
          String? classId = responseJson['classs']?['_id'];
          print('🔍 DEBUG: Extracted new class ID: $classId');
          return classId;
        } catch (e) {
          print('🚨 ERROR: Failed to parse addClass response: $e');
          return null;
        }
      } else {
        // Handle server error
        throw Exception(
            'Failed to add class: ${await response.stream.bytesToString()}');
      }
    } catch (e) {
      throw Exception(e.toString());
    }
  }

  @override
  Future<bool> deleteClass({required String id}) async {
    try {
      String uri = "$device/api/class/$id";

      var response = await http.delete(Uri.parse(uri));
      print(response.statusCode);
      if (response.statusCode >= 200 && response.statusCode <= 300) {
        return true;
      } else {
        return false;
      }
    } catch (e) {
      throw Exception(e.toString());
    }
  }

  @override
  Future<List<ClassModel>> getAllClassByCoachId(String coachId) async {
    try {
      var response = await apiService.get("/api/class/coach/$coachId");
      if (response is List) {
        return response.map((json) => ClassModel.fromJson(json)).toList();
      } else {
        throw ServerException(
            'Failed to fetch classes: ${response.statusCode}');
      }
    } catch (e) {
      throw ServerException(e.toString());
    }
  }

  @override
  Future<bool> updateClass(
      {required String classId,
      required Map<String, dynamic> updatedData}) async {
    try {
      if (device.isEmpty || classId.isEmpty) {
        throw Exception('Device or classId cannot be empty');
      }

      String uri = "$device/api/class/$classId";
      var response = await http.put(Uri.parse(uri),
          headers: {
            'Content-Type': 'application/json', // Set content type for JSON
          },
          body: json.encode(updatedData));
      if (response.statusCode >= 200 && response.statusCode <= 300) {
        print(response.statusCode);
        return true;
      } else {
        throw Exception(
            'Failed to Add time slot to Class: ${response.statusCode}');
      }
    } catch (e) {
      print(e.toString());
      throw Exception(e.toString());
    }
  }

  @override
  Future<List<EventModel>> getAllEvents(
      {required String filterType,
      required String filterValue,
      String? date}) async {
    try {
      print('hi $date');
      String uri = ApiUtils.buildEventUrl(
        device: device,
        filterType: filterType,
        filterValue: filterValue,
        date: date,
      );
      var response = await http.get(Uri.parse(uri));
      print("uri $uri");
      if (response.statusCode >= 200 && response.statusCode <= 300) {
        final List<dynamic> jsonData = jsonDecode(response.body);
        print(jsonData);
        List<EventModel> events = jsonData
            .map((e) => EventModel.fromJson(e as Map<String, dynamic>))
            .toList();

        print('got dipe');
        print(events.length);
        return events;
      } else {
        throw Exception('Failed to fetch Events: ${response.statusCode}');
      }
    } catch (e) {
      throw Exception(e.toString());
    }
  }

  @override
  Future<List<String>> getEventDates(
      {required String filterType, required String filterValue}) async {
    // Special handling for coach calendars since the API doesn't return dates properly for coaches
    if (filterType == 'coachId') {
      print(
          '🔍 getEventDates: Using comprehensive coach handling for coachId: $filterValue');
      return _getCoachEventDatesComprehensive(filterValue);
    }

    // Original logic for center calendars
    String uri = ApiUtils.buildEventUrl(
      device: device,
      filterType: filterType,
      filterValue: filterValue,
    );
    print('🔍 getEventDates API call: $uri');
    print('🔍 filterType: $filterType, filterValue: $filterValue');

    return http.get(Uri.parse(uri)).then((response) {
      print('🔍 getEventDates response status: ${response.statusCode}');

      if (response.statusCode >= 200 && response.statusCode <= 300) {
        final List<dynamic> jsonData = jsonDecode(response.body);
        print('🔍 getEventDates response data: $jsonData');
        print('🔍 Response length: ${jsonData.length}');

        if (jsonData.isEmpty) {
          print('🔍 Empty response, returning empty list');
          return <String>[];
        }

        // Check if the response is already a list of date strings (for center)
        // or if it's a list of event objects (for coach)
        if (jsonData.first is String) {
          print('🔍 Response contains strings (center case)');
          List<String> dates = jsonData.map((e) => e.toString()).toList();
          print('🔍 Returning dates: $dates');
          return dates;
        } else {
          print('🔍 Response contains objects (coach case)');
          // Coach calendar case - API returns event objects, extract dates
          Set<String> uniqueDates = <String>{};
          for (var eventData in jsonData) {
            print('🔍 Processing event data: $eventData');
            if (eventData is Map<String, dynamic> &&
                eventData['date'] != null) {
              try {
                DateTime eventDate = DateTime.parse(eventData['date']);
                String dateString = eventDate.toIso8601String();
                uniqueDates.add(dateString);
                print('🔍 Added date: $dateString');
              } catch (e) {
                print('🔍 Error parsing date: ${eventData['date']}, error: $e');
              }
            }
          }
          List<String> finalDates = uniqueDates.toList();
          print('🔍 Final unique dates: $finalDates');
          return finalDates;
        }
      } else {
        print('🔍 API error: ${response.statusCode}');
        throw Exception('Failed to fetch event dates: ${response.statusCode}');
      }
    });
  }

  Future<List<String>> _getCoachEventDatesComprehensive(String coachId) async {
    print(
        '🔍 _getCoachEventDatesComprehensive: Getting events for coach $coachId');

    // Get events for a 3-month range (current month, previous month, and next month)
    DateTime now = DateTime.now();
    DateTime startDate = DateTime(now.year, now.month - 1, 1); // Previous month
    DateTime endDate =
        DateTime(now.year, now.month + 2, 0); // End of next month

    Set<String> uniqueDates = <String>{};
    List<Future<void>> futures = [];

    print(
        '🔍 Fetching coach events from ${startDate.toIso8601String().split('T')[0]} to ${endDate.toIso8601String().split('T')[0]}');

    // Create a list of dates to check
    List<DateTime> datesToCheck = [];
    DateTime currentMonthStart = DateTime(now.year, now.month, 1);
    DateTime currentMonthEnd = DateTime(now.year, now.month + 1, 0);

    for (DateTime date = startDate;
        date.isBefore(endDate);
        date = date.add(Duration(days: 1))) {
      // Check every day for current month, every 3 days for other months
      if (date.isAfter(currentMonthStart.subtract(Duration(days: 1))) &&
          date.isBefore(currentMonthEnd.add(Duration(days: 1)))) {
        // Current month - check every day
        datesToCheck.add(date);
      } else if ((date.difference(startDate).inDays % 3) == 0) {
        // Other months - sample every 3 days
        datesToCheck.add(date);
      }
    }

    // Add some specific dates we know might have events
    datesToCheck.add(DateTime(2025, 7, 22)); // The date we know has events
    datesToCheck.add(now); // Today

    // Remove duplicates and sort
    datesToCheck = datesToCheck.toSet().toList();
    datesToCheck.sort();

    print('🔍 Will check ${datesToCheck.length} dates for coach events');

    // Fetch events for each date concurrently (but limit concurrency)
    for (int i = 0; i < datesToCheck.length; i += 5) {
      // Process 5 dates at a time
      List<DateTime> batch = datesToCheck.skip(i).take(5).toList();

      for (DateTime date in batch) {
        futures.add(_fetchEventsForDate(coachId, date, uniqueDates));
      }

      // Wait for this batch to complete before starting the next
      await Future.wait(futures);
      futures.clear();
    }

    List<String> result = uniqueDates.toList();
    result.sort(); // Sort dates chronologically

    print(
        '🔍 _getCoachEventDatesComprehensive: Found ${result.length} unique event dates');
    print('🔍 Event dates: $result');

    return result;
  }

  Future<void> _fetchEventsForDate(
      String coachId, DateTime date, Set<String> uniqueDates) async {
    String dateString =
        date.toIso8601String().split('T')[0]; // YYYY-MM-DD format

    try {
      String uri = ApiUtils.buildEventUrl(
        device: device,
        filterType: 'coachId',
        filterValue: coachId,
        date: dateString,
      );

      var response = await http.get(Uri.parse(uri));

      if (response.statusCode >= 200 && response.statusCode <= 300) {
        final List<dynamic> jsonData = jsonDecode(response.body);

        if (jsonData.isNotEmpty) {
          // If there are events on this date, add it to our unique dates
          for (var eventData in jsonData) {
            if (eventData is Map<String, dynamic> &&
                eventData['date'] != null) {
              try {
                DateTime eventDate = DateTime.parse(eventData['date']);
                uniqueDates.add(eventDate.toIso8601String());
              } catch (e) {
                print('🔍 Error parsing event date: ${eventData['date']}');
              }
            }
          }
        }
      }
    } catch (e) {
      print('🔍 Error fetching events for date $dateString: $e');
      // Continue with other dates even if one fails
    }
  }

  @override
  Future<EventDatesModel> getEventsByClassId({required String classId}) async {
    try {
      final String uri = "$device/api/events/class/$classId";
      print("Fetching from: $uri");

      final response = await http.get(Uri.parse(uri));

      if (response.statusCode >= 200 && response.statusCode <= 300) {
        final Map<String, dynamic> jsonData = jsonDecode(response.body);
        print(jsonData);
        List<EventDate>? eventDates;

        if (jsonData['eventDates'] != null) {
          eventDates = (jsonData['eventDates'] as List)
              .map((e) => EventDate.fromJson(e as Map<String, dynamic>))
              .toList();
        }

        print('got it');
        return EventDatesModel(eventDates: eventDates);
      } else {
        throw Exception('Failed to fetch Events: ${response.statusCode}');
      }
    } catch (e) {
      print("Error fetching events: $e");
      throw Exception('Error fetching events: $e');
    }
  }

  @override
  Future<List<EventModel>> getEventsByDate({required String id}) async {
    try {
      print('event By date');
      String date =
          DateFormat("yyyy-MM-dd'T'HH:mm:ss.SSS'Z'").format(DateTime.now());
      String uri = "$device/api/events/date?center=$id&date=$date";
      print(uri);
      var response = await http.get(Uri.parse(uri));

      if (response.statusCode >= 200 && response.statusCode <= 300) {
        final List<dynamic> jsonData = jsonDecode(response.body);
        //  debugPrint(jsonData.toString(), wrapWidth: 1024);
        List<EventModel> events = jsonData
            .map((e) => EventModel.fromJson(e as Map<String, dynamic>))
            .toList();
        print('no error');
        return events;
      } else {
        throw Exception('Failed to fetch Events: ${response.statusCode}');
      }
    } catch (e) {
      throw Exception(e.toString());
    }
  }

  @override
  Future<EventModel> getEventsByEventId({required String eventId}) {
    // TODO: implement getEventsByEventId
    throw UnimplementedError();
  }

  @override
  Future<bool> deleteEventsbyEventId({required String eventId}) async {
    try {
      String uri = "$device/api/events/event/$eventId";
      var response = await http.delete(Uri.parse(uri));
      if (response.statusCode >= 200 && response.statusCode <= 300)
        return true;
      else
        throw Exception('Failed to delete event: ${response.statusCode}');
    } catch (e) {
      throw Exception(e.toString());
    }
  }

  @override
  Future<AttendanceModel> postAttendance(
      {String? classId,
      String? code,
      String? qrCodeData,
      String? classDate}) async {
    try {
      String uri = "$device/api/attendance/verify";

      // Create a map with non-null parameters
      Map<String, String> body = {};
      if (classId != null) body['classId'] = classId;
      if (code != null) body['code'] = code;
      if (qrCodeData != null) body['qrCodeData'] = qrCodeData;
      body['classDate'] =
          classDate != null ? classDate : DateTime.now().toString();
      print(body);
      var response = await http.post(
        Uri.parse(uri),
        headers: {
          'Content-Type': 'application/json', // Set content type for JSON
        },
        body: jsonEncode(body), // Convert map to JSON string
      );

      if (response.statusCode >= 200 && response.statusCode < 300) {
        print(response.statusCode);
        // Parse response body and return AttendanceModel
        return AttendanceModel.fromJson(jsonDecode(response.body));
      } else {
        throw Exception('Failed to post attendance: ${response.statusCode}');
      }
    } catch (e) {
      throw Exception(e.toString());
    }
  }

  @override
  Future<List<AttendanceModel>> getPresentAttendance(
      {required String classId, required String classDate}) async {
    try {
      String uri = "$device/api/attendance/present/$classId/$classDate";
      print(uri);
      var response = await http.get(Uri.parse(uri));

      if (response.statusCode >= 200 && response.statusCode < 300) {
        print(response.statusCode);
        List<dynamic> jsonData = jsonDecode(response.body);
        List<AttendanceModel> attendances = jsonData
            .map((e) => AttendanceModel.fromJson(e as Map<String, dynamic>))
            .toList();
        return attendances;
      } else {
        throw Exception('Failed to post attendance: ${response.statusCode}');
      }
    } catch (e) {
      throw Exception(e.toString());
    }
  }

  @override
  Future<List<PendingModel>> getPendingReview({required String id}) async {
    try {
      String uri = "$device/api/review/student/pending/$id";
      print(uri);
      var response = await http.get(Uri.parse(uri));
      if (response.statusCode >= 200 && response.statusCode < 300) {
        //print(response.body);
        List<dynamic> jsonData = jsonDecode(response.body);
        print("object");
        print(jsonData);
        List<PendingModel> pendings =
            jsonData.map((e) => PendingModel.fromJson(e)).toList();
        print(pendings);
        return pendings;
      } else
        throw Exception(
            'Failed to Fetch pending students ${response.statusCode}');
    } catch (e) {
      throw Exception(e.toString());
    }
  }

  @override
  Future<List<PendingModel>> getPendingReviewByClassId({
    required String classId,
  }) async {
    try {
      final response = await apiService.get(
        '/api/review/student/pending/class/$classId',
      );

      if (response is List) {
        return response.map((e) => PendingModel.fromJson(e)).toList();
      } else {
        throw ServerException("Unexpected response format: $response");
      }
    } catch (e) {
      throw ServerException("Unexpected error: $e");
    }
  }

  @override
  Future<List<PendingModel>> getPendingReviewByCoachId({
    required String coachId,
  }) async {
    try {
      final response = await apiService.get(
        '/api/review/student/pending/coach/$coachId',
      );

      if (response is List) {
        return response.map((e) => PendingModel.fromJson(e)).toList();
      } else {
        throw ServerException("Unexpected response format: $response");
      }
    } catch (e) {
      throw ServerException("Unexpected error: $e");
    }
  }

  @override
  Future<List<ChildModel>> getStudentsByClassId(
      {required String classId}) async {
    try {
      // Note: classId here is actually a slotId (ClassDate ID) based on usage
      String uri = "/api/class/$classId/students";
      var response = await apiService.get(uri);
      if (response is List) {
        return response.map((json) => ChildModel.fromJson(json)).toList();
      } else if (response is Map && response.containsKey('students')) {
        // Handle case where API returns object with students array
        final List<dynamic> studentsData =
            response['students'] as List<dynamic>;
        return studentsData.map((json) => ChildModel.fromJson(json)).toList();
      } else {
        throw Exception('Failed to fetch students: Unexpected response format');
      }
    } catch (e) {
      print(e.toString());
      throw Exception(e.toString());
    }
  }

  @override
  Future<ClassModel> getCoachAndCenterByClassId(
      {required String classId}) async {
    try {
      var response = await apiService.get("/api/class/$classId/coach-center");
      print('before getting class coach centert');
      final classModel = ClassModel.fromJson(response);
      print('after getting class coach center $classModel');
      return classModel;
    } catch (e) {
      throw Exception(e.toString());
    }
  }

  @override
  Future<bool> classSlotDelete({
    required String classId,
    String? cancelType,
    String? eventId,
    String? newDate,
    String? newStartTime,
    String? newEndTime,
  }) async {
    try {
      final Map<String, dynamic> requestBody = {
        'cancelType': cancelType,
        'eventId': eventId,
      };

      // Add new date and time for rearrange
      if (cancelType == 'rearrange' && newDate != null) {
        requestBody['date'] = newDate;
        requestBody['newStartTime'] = newStartTime;
        requestBody['newEndTime'] = newEndTime;
      }

      print('🚀 FRONTEND CANCEL REQUEST:');
      print('   ClassId: $classId');
      print('   CancelType: $cancelType');
      print('   EventId: $eventId');
      print('   RequestBody: ${jsonEncode(requestBody)}');
      print('   URL: ${AppText.device}/api/events/class/$classId');

      final response = await http.delete(
        Uri.parse("${AppText.device}/api/events/class/$classId"),
        headers: {
          'Content-Type': 'application/json',
        },
        body: jsonEncode(requestBody),
      );

      print('🔄 CANCEL RESPONSE:');
      print('   Status Code: ${response.statusCode}');
      print('   Response Body: ${response.body}');

      if (response.statusCode >= 200 && response.statusCode < 300) {
        print('✅ CANCEL SUCCESS');
        return true;
      } else {
        print('❌ CANCEL FAILED - Status: ${response.statusCode}');
        throw Exception(
            'Failed to delete class slot. Status code: ${response.statusCode}, Response: ${response.body}');
      }
    } catch (e) {
      print('❌ CANCEL ERROR: $e');
      throw Exception('Error deleting class slot: $e');
    }
  }

  @override
  Future<List<String>> getRequestSendedByCenterToCoach(
      {required String centerId, required String type}) async {
    final response =
        await apiService.get("/api/center/request/$type/$centerId");
    if (response is List) {
      return response.map((e) => e.toString()).toList();
    } else {
      throw ServerException("Unexpected response format: $response");
    }
  }

  @override
  Future<List<ClassModel>> getAllClassesForParent(
      {required String centerId}) async {
    try {
      final response =
          await apiService.get('/api/class/parent/center/$centerId');
      if (response != null) {
        return (response as List)
            .map((e) => ClassModel.fromJson(e as Map<String, dynamic>))
            .toList();
      }
      return [];
    } catch (e) {
      throw ServerException(e.toString());
    }
  }

  @override
  Future<List<ClassModel>> getAllClassByCenterId(String id) async {
    try {
      var response = await apiService.get("/api/class/center/$id");
      if (response is List) {
        return response.map((json) => ClassModel.fromJson(json)).toList();
      } else {
        throw Exception('Failed to fetch classes: ${response.statusCode}');
      }
    } catch (e) {
      throw Exception(e.toString());
    }
  }

  @override
  Future<bool> updateCoachInClass({
    required String classId,
    required String coachId,
  }) async {
    try {
      final response = await apiService.put(
        '/api/class/$classId/coach',
        {
          'coachId': coachId,
        },
      );

      if (response == true ||
          (response is Map && response['success'] == true)) {
        return true;
      } else if (response is Map && response.containsKey('message')) {
        throw ServerException(response['message']);
      } else {
        throw ServerException("Failed to update coach in class");
      }
    } catch (e) {
      if (e is ServerException) rethrow;
      throw ServerException(e.toString());
    }
  }

  @override
  Future<List<ClassDate>> getAllSlotsByClassId(String classId) async {
    final String uri = "$device/api/class/$classId/slots";
    final response = await http.get(Uri.parse(uri));
    if (response.statusCode >= 200 && response.statusCode <= 300) {
      final Map<String, dynamic> jsonData = jsonDecode(response.body);
      if (jsonData['success'] == true && jsonData['slots'] is List) {
        return (jsonData['slots'] as List)
            .map((e) => ClassDate.fromJson(e))
            .toList();
      } else {
        throw Exception(
            jsonData['message'] ?? 'Failed to fetch slots: success false');
      }
    } else {
      throw Exception('Failed to fetch slots: ${response.statusCode}');
    }
  }

  @override
  Future<bool> deleteSlotById(String slotId) async {
    final String uri = "$device/api/class/slot/$slotId";
    final response = await http.delete(Uri.parse(uri));
    if (response.statusCode >= 200 && response.statusCode < 300) {
      return true;
    } else {
      throw Exception('Failed to delete slot: ${response.statusCode}');
    }
  }

  @override
  Future<bool> deleteEventById(String eventId) async {
    try {
      String uri = "$device/api/events/event/$eventId";
      var response = await http.delete(Uri.parse(uri));
      if (response.statusCode >= 200 && response.statusCode <= 300)
        return true;
      else
        throw Exception('Failed to delete event: ${response.statusCode}');
    } catch (e) {
      throw Exception(e.toString());
    }
  }
}
