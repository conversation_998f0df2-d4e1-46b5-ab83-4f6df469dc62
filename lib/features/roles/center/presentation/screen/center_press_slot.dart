import 'package:class_z/core/imports.dart';
import 'package:class_z/core/widgets/calculate_final_Price.dart';

class CenterPressSlot extends StatelessWidget {
  final EventModel event;
  const CenterPressSlot({required this.event, super.key});

  void _handleCancelSlot(BuildContext context) {
    print('🔥 CANCEL BUTTON PRESSED!');
    final studentCount = event.dateId?.students.length;
    print('🔥 Student count: $studentCount');
    print('🔥 Event ID: ${event.id}');
    print('🔥 Class ID: ${event.classId?.id}');

    if (studentCount > 0) {
      // Students are enrolled - show cancellation options
      print('🔥 Showing cancel dialog for enrolled students');
      _showCancelDialog(context);
    } else {
      // No students enrolled - show simple cancellation confirmation
      print('🔥 Showing simple cancel dialog for no students');
      _showSimpleCancelDialog(context);
    }
  }

  void _showCancelDialog(BuildContext context) {
    print('🔥 _showCancelDialog called');
    showDialog(
      context: context,
      builder: (BuildContext context) {
        print('🔥 CancelSlotDialog builder called');
        return CancelSlotDialog(event: event);
      },
    );
  }

  void _showSimpleCancelDialog(BuildContext context) {
    print('🔥 _showSimpleCancelDialog called');
    showDialog(
      context: context,
      builder: (BuildContext context) {
        print('🔥 Simple cancel dialog builder called');
        return Dialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(20.0),
          ),
          child: Container(
            padding: EdgeInsets.all(20.w),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                customtext(
                  context: context,
                  newYear: "cancel Slot",
                  font: 20.sp,
                  weight: FontWeight.w600,
                ),
                SizedBox(height: 20.h),
                customtext(
                  context: context,
                  newYear:
                      "Are you sure you want to cancel this slot? No students are enrolled, so this action can be completed immediately.",
                  font: 16.sp,
                  weight: FontWeight.w400,
                  textAlign: TextAlign.center,
                ),
                SizedBox(height: 30.h),
                Row(
                  children: [
                    Expanded(
                      child: Button(
                        buttonText: "Keep Slot",
                        onPressed: () => NavigatorService.goBack(),
                        color: AppPallete.greyColor,
                        fontWeight: FontWeight.w600,
                        textSize: 15.sp,
                        height: 45.h,
                      ),
                    ),
                    SizedBox(width: 15.w),
                    Expanded(
                      child: Button(
                        buttonText: "cancel Slot",
                        onPressed: () {
                          if (event.id == null) {
                            errorState(
                                context: context,
                                error: 'Event ID is missing.');
                            return;
                          }
                          NavigatorService.goBack();
                          context.read<CenterBloc>().add(
                                DeleteEventByIdEvent(eventId: event.id!),
                              );
                        },
                        color: AppPallete.secondaryColor,
                        fontWeight: FontWeight.w600,
                        textSize: 15.sp,
                        height: 45.h,
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    final sharedRepository = Provider.of<SharedRepository>(context);
    UserModel? user = sharedRepository.getUserData();
    print(event.dateId);

    // Debug: Print coach information
    print('🔍 DEBUG CenterPressSlot: Event ID: ${event.id}');
    print('🔍 DEBUG CenterPressSlot: Coach object: ${event.coach}');
    print(
        '🔍 DEBUG CenterPressSlot: Coach displayName: ${event.coach?.displayName}');
    print('🔍 DEBUG CenterPressSlot: Coach ID: ${event.coach?.id}');
    return Scaffold(
      appBar: CustomAppBarDouble(
        title: "My Slot",
        title2: "Class details",
        leading: customBackButton(),
      ),
      body: BlocListener<CenterBloc, CenterState>(
        listener: (context, state) {
          if (state is CenterLoadingState)
            loadingState(context: context);
          else
            hideLoadingDialog(context);
          if (state is CenterErrorState)
            errorState(context: context, error: state.message);
          if (state is ClassSlotDeleteSuccessState) {
            errorState(
                context: context, error: state.message ?? 'Slot cancelled');
            NavigatorService.goBack();
          }
        },
        child: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              SizedBox(
                height: 53.h,
              ),
              centerSlotConfirmationCard(
                  context: context,
                  firstColor: AppPallete.color185,
                  firstText: () {
                    final currentStudents = (event.classId?.student is List)
                        ? (event.classId!.student as List).length
                        : 0;
                    final maxStudents = event.scheduleNumberOfStudent ??
                        event.classId?.numberOfStudent ??
                        0;

                    if (currentStudents >= maxStudents && maxStudents > 0) {
                      return "Program Filled up";
                    } else if (currentStudents > 0) {
                      return "Recruiting...";
                    } else {
                      return "Open for enrollment";
                    }
                  }(),
                  address: () {
                    final classAddress = event.classId?.address ?? '';
                    // Check if it's a placeholder text or empty
                    if (classAddress.isEmpty ||
                        classAddress.toLowerCase().contains('full address') ||
                        classAddress
                            .toLowerCase()
                            .contains('corresponding program')) {
                      return 'center'; // This will show "On-site coaching: [center name]"
                    }
                    return classAddress;
                  }(),
                  firstTextColor: Colors.black,
                  date: event.date != null
                      ? DateFormat('dd MMM yyyy').format(event.date!)
                      : "Date to be scheduled",
                  center: user?.data?.center?.displayName ?? "",
                  course: event.title ??
                      "${event.classId?.classProviding ?? 'Class'} (${event.classId?.level ?? 'Level'})",
                  classTime: (event.durationMinutes != null &&
                          (event.durationMinutes?.isNotEmpty ?? false))
                      ? event.durationMinutes!
                      : "",
                  time: event.startTime != null && event.endTime != null
                      ? "${event.startTime} - ${event.endTime}"
                      : "To be scheduled",
                  currentStudent: event.dateId?.students.length.toString() ??
                      "0".toString(),
                  numberOfStudent: (event.scheduleNumberOfStudent ??
                          event.classId?.numberOfStudent ??
                          0)
                      .toString(),
                  ageStart: event.classId?.ageFrom?.toString() ?? "",
                  agefinish: event.classId?.ageTo?.toString() ?? "",
                  coach: event.coach?.displayName ?? "Coach TBD",
                  charges: (event.scheduleCharge ?? event.classId?.charge) !=
                          null
                      ? "HKD ${((event.scheduleCharge ?? event.classId?.charge ?? 0) * 25).toStringAsFixed(0)}"
                      : "",
                  senFriendly: event.classId?.sen ?? false),
              SizedBox(
                height: 24.h,
              ),

              // Header Row
              // event.classId?.student.length > 0
              //     ? _makeBillBreakDown(context: context)
              //     : SizedBox.shrink(),
              _makeBillBreakDown(context: context),
              SizedBox(height: 13.h),

              SizedBox(
                height: 24.h,
              ),
              customDivider(width: 430.w, padding: 0),
              Padding(
                padding: EdgeInsets.only(
                    left: 22.w, right: 31.w, bottom: 5.h, top: 12.h),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Button(
                        buttonText: "student list",
                        onPressed: () {
                          // Validate that we have a valid slot ID before navigating
                          final slotId = event.dateId?.id ?? '';
                          if (slotId.isEmpty) {
                            errorState(
                                context: context,
                                error:
                                    'Cannot access student list: No slot ID available. Please ensure this class has scheduled dates.');
                            return;
                          }

                          NavigatorService.pushNamed(AppRoutes.studentList,
                              arguments: {
                                'classId': event.classId?.id,
                                'slotId': slotId,
                                'title':
                                    '${event.classId?.classProviding} (${event.classId?.level})',
                                'numberOfStudent':
                                    event.dateId?.students.length,
                                'totalStudent':
                                    event.dateId?.students?.length ?? 0,
                                'message': true
                              });
                        },
                        color: AppPallete.secondaryColor,
                        fontWeight: FontWeight.w700,
                        textSize: 15.sp,
                        width: 174.w,
                        shadows: [shadow(blurRadius: 15, opacity: 0.1)],
                        height: 49.h),
                    Button(
                      buttonText: "cancel slot",
                      color: AppPallete.white,
                      textColorFinal: AppPallete.secondaryColor,
                      fontWeight: FontWeight.w700,
                      textSize: 15.sp,
                      width: 174.w,
                      shadows: [shadow(blurRadius: 15, opacity: 0.1)],
                      height: 49.h,
                      onPressed: () {
                        print('🔥 Cancel button onPressed triggered');
                        _handleCancelSlot(context);
                      },
                    ),
                  ],
                ),
              )
            ],
          ),
        ),
      ),
    );
  }

  Widget _makeBillBreakDown({required BuildContext context}) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: EdgeInsets.only(left: 22.w),
          child: customtext(
              context: context,
              newYear: "Bill breakdown",
              font: 20.sp,
              weight: FontWeight.w600),
        ),
        SizedBox(
          height: 18.h,
        ),
        InvoiceSection(
          rows: [
            DataRow(cells: [
              DataCell(customtext(
                  context: context,
                  newYear:
                      "${event.classId?.classProviding} (${event.classId?.level})",
                  font: 13.sp,
                  weight: FontWeight.w500)),
              DataCell(Center(
                  child: customtext(
                      context: context,
                      newYear:
                          "${event.dateId?.students.length.toString() ?? "0"}",
                      font: 13.sp,
                      weight: FontWeight.w500))),
              DataCell(Center(
                  child: (event.scheduleCharge ?? event.classId?.charge) != null
                      ? Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            customtext(
                                context: context,
                                newYear: "HKD",
                                font: 13.sp,
                                weight: FontWeight.w500),
                            SizedBox(width: 4.w),
                            customtext(
                                context: context,
                                newYear:
                                    "${((event.scheduleCharge ?? event.classId?.charge ?? 0) * 25).toStringAsFixed(0)}",
                                font: 15.sp,
                                weight: FontWeight.w500),
                          ],
                        )
                      : Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            customtext(
                                context: context,
                                newYear: "HKD",
                                font: 13.sp,
                                weight: FontWeight.w500),
                            SizedBox(width: 4.w),
                            customtext(
                                context: context,
                                newYear: "0",
                                font: 15.sp,
                                weight: FontWeight.w500),
                          ],
                        ))),
              DataCell(Align(
                alignment: Alignment.centerRight,
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    customtext(
                        context: context,
                        newYear: "HKD",
                        font: 13.sp,
                        weight: FontWeight.w500),
                    SizedBox(width: 4.w),
                    customtext(
                        context: context,
                        newYear: () {
                          final studentCount = (event.dateId?.students is List)
                              ? (event.dateId!.students as List).length
                              : 0;
                          final charge = event.scheduleCharge ??
                              event.classId?.charge ??
                              0;
                          final total = studentCount *
                              (num.tryParse(charge.toString()) ?? 0) *
                              25; // Convert to HKD
                          return total.toStringAsFixed(0);
                        }(),
                        font: 15.sp,
                        weight: FontWeight.w500),
                  ],
                ),
              )),
            ]),
            // more rows...
          ],
          subtotal: finalPrice(
            (event.dateId?.students is List)
                ? (event.dateId!.students as List).length
                : 0,
            event.scheduleCharge ?? event.classId?.charge ?? 0,
          ).toDouble(),
        )
      ],
    );
  }
}

class CancelSlotDialog extends StatefulWidget {
  final EventModel event;

  const CancelSlotDialog({required this.event, super.key});

  @override
  State<CancelSlotDialog> createState() => _CancelSlotDialogState();
}

class _CancelSlotDialogState extends State<CancelSlotDialog> {
  DateTime? selectedDate;
  bool showRearrangeOptions = false;

  Future<void> _selectDate(BuildContext context) async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: selectedDate ?? DateTime.now(),
      firstDate: DateTime.now(),
      lastDate: DateTime.now().add(const Duration(days: 365)),
    );
    if (picked != null && picked != selectedDate) {
      setState(() {
        selectedDate = picked;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Dialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(20.0),
      ),
      child: Container(
        padding: EdgeInsets.all(20.w),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            customtext(
              context: context,
              newYear: "cancel Slot",
              font: 20.sp,
              weight: FontWeight.w600,
            ),
            SizedBox(height: 20.h),
            if (!showRearrangeOptions) ...[
              customtext(
                context: context,
                newYear: "Choose an option for cancellation:",
                font: 16.sp,
                weight: FontWeight.w400,
              ),
              SizedBox(height: 30.h),
              Button(
                buttonText: "Refund Students",
                onPressed: () {
                  NavigatorService.goBack();
                  context.read<CenterBloc>().add(
                        ClassSlotDeleteEvent(
                          widget.event.classId?.id ?? '',
                          cancelType: 'refund',
                          eventId: widget.event.id,
                        ),
                      );
                },
                color: AppPallete.secondaryColor,
                fontWeight: FontWeight.w600,
                textSize: 15.sp,
                width: double.infinity,
                height: 45.h,
              ),
              SizedBox(height: 15.h),
              Button(
                buttonText: "Rearrange Slot",
                onPressed: () {
                  setState(() {
                    showRearrangeOptions = true;
                  });
                },
                color: AppPallete.white,
                textColorFinal: AppPallete.secondaryColor,
                fontWeight: FontWeight.w600,
                textSize: 15.sp,
                width: double.infinity,
                height: 45.h,
              ),
            ] else ...[
              customtext(
                context: context,
                newYear: "Select new date:",
                font: 16.sp,
                weight: FontWeight.w400,
              ),
              SizedBox(height: 20.h),

              // Date Selection
              InkWell(
                onTap: () => _selectDate(context),
                child: Container(
                  padding:
                      EdgeInsets.symmetric(horizontal: 12.w, vertical: 12.h),
                  decoration: BoxDecoration(
                    border: Border.all(color: AppPallete.darkGrey),
                    borderRadius: BorderRadius.circular(8.r),
                  ),
                  child: Row(
                    children: [
                      Icon(Icons.calendar_today, color: AppPallete.darkGrey),
                      SizedBox(width: 8.w),
                      Expanded(
                        child: customtext(
                          context: context,
                          newYear: selectedDate != null
                              ? DateFormat('dd MMM yyyy').format(selectedDate!)
                              : "Select Date",
                          font: 14.sp,
                          weight: FontWeight.w400,
                          color: selectedDate != null
                              ? AppPallete.black
                              : AppPallete.darkGrey,
                        ),
                      ),
                    ],
                  ),
                ),
              ),
              SizedBox(height: 20.h),

              // Confirm Rearrange Button
              Button(
                buttonText: "Confirm Rearrange",
                onPressed: selectedDate != null
                    ? () {
                        NavigatorService.goBack();

                        // Send new date to backend for rearrangement
                        context.read<CenterBloc>().add(
                              ClassSlotDeleteEvent(
                                widget.event.classId?.id ?? '',
                                cancelType: 'rearrange',
                                eventId: widget.event.id,
                                newDate: selectedDate != null
                                    ? DateFormat('yyyy-MM-dd')
                                        .format(selectedDate!)
                                    : null,
                              ),
                            );
                      }
                    : null,
                color: selectedDate != null
                    ? AppPallete.secondaryColor
                    : AppPallete.greyColor,
                fontWeight: FontWeight.w600,
                textSize: 15.sp,
                width: double.infinity,
                height: 45.h,
              ),
            ],
            SizedBox(height: 15.h),
            TextButton(
              onPressed: () => NavigatorService.goBack(),
              child: customtext(
                context: context,
                newYear: "Cancel",
                font: 14.sp,
                weight: FontWeight.w500,
                color: Colors.grey,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
